2025-08-26 15:13:37.431509: 61
2025-08-26 15:13:37.435495: ✅ 数据库更新成功: planConfig
2025-08-26 15:13:37.435495: ✅ 数据库更新成功: SysConfig
2025-08-26 15:13:37.435495: ✅ 数据库更新成功: Sip2Config
2025-08-26 15:13:37.435495: ✅ 数据库更新成功: pageConfig
2025-08-26 15:13:37.435495: ✅ 数据库更新成功: readerConfig
2025-08-26 15:13:37.435495: ✅ 数据库更新成功: banZhengConfig
2025-08-26 15:13:37.436492: ✅ 数据库更新成功: printConfig
2025-08-26 15:13:37.650782: 开始初始化闸机协调器...
2025-08-26 15:13:37.651785: ✅ 已清除串口配置缓存，下次访问将重新从数据库读取
2025-08-26 15:13:37.652776: ✅ 已清除 SettingProvider 串口配置缓存
2025-08-26 15:13:37.668723: ✅ 通过 getSerialConfig 获取串口配置: COM1 @ 115200
2025-08-26 15:13:37.669719: ✅ 串口配置加载完成: COM1 @ 115200
2025-08-26 15:13:37.669719: 可用串口: [COM1, COM2, COM3, COM4, COM5, COM6]
2025-08-26 15:13:37.673706: 连接闸机串口: COM1
2025-08-26 15:13:37.673706: 尝试连接串口: COM1, 波特率: 115200
2025-08-26 15:13:37.673706: 串口连接成功: COM1 at 115200 baud
2025-08-26 15:13:37.673706: 开始监听串口数据
2025-08-26 15:13:37.673706: 串口连接状态变化: true
2025-08-26 15:13:37.674709: 闸机串口连接成功
2025-08-26 15:13:37.674709: 串口 COM1 连接成功 (波特率: 115200)
2025-08-26 15:13:37.674709: 闸机串口服务初始化成功: COM1
2025-08-26 15:13:37.674709: 开始监听串口数据（通过 GateSerialManager 事件流）
2025-08-26 15:13:37.674709: 开始监听闸机串口命令
2025-08-26 15:13:37.674709: 开始初始化RFID服务和共享池...
2025-08-26 15:13:37.674709: 开始初始化增强RFID服务...
2025-08-26 15:13:37.674709: 开始初始化增强RFID服务...
2025-08-26 15:13:37.675699: SIP2图书信息服务初始化完成
2025-08-26 15:13:37.675699: 增强RFID服务初始化完成，配置了1个阅读器
2025-08-26 15:13:37.675699: 📋 从数据库读取主从机配置: channel_1
2025-08-26 15:13:37.676696: 📋 配置详情: 主机模式
2025-08-26 15:13:37.676696: 🚀 主机模式：启动RFID硬件持续扫描
2025-08-26 15:13:37.676696: 启动RFID持续扫描...
2025-08-26 15:13:37.676696: changeReaders
2025-08-26 15:13:37.676696: createIsolate isOpen:false,isOpening:false
2025-08-26 15:13:37.677693: createIsolate newport null
2025-08-26 15:13:37.699622: socket 连接成功,isBroadcast:false
2025-08-26 15:13:37.699622: changeSocketStatus:true
2025-08-26 15:13:37.700623: Sip2HeartBeatManager start loginACS:false askACS:false
2025-08-26 15:13:37.700623: Req msgType：Sip2MsgType.login ,length:72， ret:  9300CNhlsp_sip2|COsip2|CP3a40852d-49fd-4df2-a1f9-6e2641a6e91f|AY1AZEC16
2025-08-26 15:13:38.135775: Rsp : 941AY1AZFDFC
2025-08-26 15:13:38.158702: loginRsp:{OK: 1, MsgSeqId: 1AZFDFC}
2025-08-26 15:13:38.160704: Sip2HeartBeatManager start loginACS:false askACS:true
2025-08-26 15:13:38.160704: 发送心跳
2025-08-26 15:13:38.161693: Req msgType：Sip2MsgType.scStatus ,length:20， ret:  9900522.00AY2AZFC9F
2025-08-26 15:13:38.179631: 找到网口配置: LSGate图书馆安全门RFID阅读器 - **************:6012
2025-08-26 15:13:38.180627: 使用网口连接: **************:6012
2025-08-26 15:13:38.180627: open():SendPort
2025-08-26 15:13:38.181623: untilDetcted():SendPort
2025-08-26 15:13:38.181623: 网口连接成功: **************:6012
2025-08-26 15:13:38.181623: startInventory():SendPort
2025-08-26 15:13:38.182621: RFID硬件扫描已启动，阅读器开始持续工作
2025-08-26 15:13:38.182621: RFID持续扫描启动完成
2025-08-26 15:13:38.183620: 增强RFID服务初始化完成，持续扫描已启动
2025-08-26 15:13:38.183620: 📋 从数据库读取主从机配置: channel_1
2025-08-26 15:13:38.184613: 📋 配置详情: 主机模式
2025-08-26 15:13:38.184613: 🚀 主机模式：启动持续数据收集，数据将持续进入共享池
2025-08-26 15:13:38.184613: 🎯 关键修复：使用轮询机制确保标签持续被发现
2025-08-26 15:13:38.184613: 🧹 清空RFID缓冲区（保持tagList），确保数据收集正常工作...
2025-08-26 15:13:38.185610: 清空RFID扫描缓冲区...
2025-08-26 15:13:38.185610: 🧹 已清空HWTagProvider: 0 -> 0个标签
2025-08-26 15:13:38.185610: 🔧 主机模式：清空软件缓冲区，保持硬件持续扫描
2025-08-26 15:13:38.185610: 🔄 开始重置已处理条码集合...
2025-08-26 15:13:38.186607: 📊 重置前状态: 大小=0, 内容=[]
2025-08-26 15:13:38.186607: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-26 15:13:38.186607: 🔄 当前场上标签将被重新识别为新标签
2025-08-26 15:13:38.186607: 📊 当前tagList状态: 0个标签
2025-08-26 15:13:38.186607: ⚠️ 当前未在扫描状态，跳过立即轮询
2025-08-26 15:13:38.186607: ✅ 软件缓冲区已清空，硬件扫描保持运行
2025-08-26 15:13:38.187602: 🚀 启动增强数据收集（事件监听 + 轮询备用）...
2025-08-26 15:13:38.187602: 🚀 开始RFID数据收集...
2025-08-26 15:13:38.187602: 📋 扫描结果和缓存已清空
2025-08-26 15:13:38.188599: 清空RFID扫描缓冲区...
2025-08-26 15:13:38.188599: 🧹 已清空HWTagProvider: 0 -> 0个标签
2025-08-26 15:13:38.188599: 🔧 主机模式：清空软件缓冲区，保持硬件持续扫描
2025-08-26 15:13:38.188599: 🔄 开始重置已处理条码集合...
2025-08-26 15:13:38.188599: 📊 重置前状态: 大小=0, 内容=[]
2025-08-26 15:13:38.188599: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-26 15:13:38.188599: 🔄 当前场上标签将被重新识别为新标签
2025-08-26 15:13:38.189596: 📊 当前tagList状态: 0个标签
2025-08-26 15:13:38.189596: ⚠️ 当前未在扫描状态，跳过立即轮询
2025-08-26 15:13:38.189596: ✅ 软件缓冲区已清空，硬件扫描保持运行
2025-08-26 15:13:38.189596: 🎯 启动数据监听和轮询机制...
2025-08-26 15:13:38.190592: 🚀 标签轮询机制已启动 (每500ms轮询一次)
2025-08-26 15:13:38.190592: ✅ 标签监听改为仅轮询机制（每500ms轮询tagList）
2025-08-26 15:13:38.190592: 📊 当前HWTagProvider状态:
2025-08-26 15:13:38.190592:   - tagList: 0个标签
2025-08-26 15:13:38.190592:   - type: HWTagType.tagList
2025-08-26 15:13:38.190592: 🎯 标签数据获取已统一为轮询机制
2025-08-26 15:13:38.190592: ✅ RFID数据收集已启动，轮询机制运行中
2025-08-26 15:13:38.190592: 📊 当前tagList状态: 0个标签
2025-08-26 15:13:38.190592: subThread :ReaderCommand.readerList
2025-08-26 15:13:38.191589: commandRsp:ReaderCommand.readerList
2025-08-26 15:13:38.191589: readerList：1,readerSetting：1
2025-08-26 15:13:38.191589: cacheUsedReaders:1
2025-08-26 15:13:38.191589: subThread :ReaderCommand.open
2025-08-26 15:13:38.191589: commandRsp:ReaderCommand.open
2025-08-26 15:13:38.191589: LSGate使用网络连接 IP: **************, Port: 6012, DeviceType: LSGControlCenter
2025-08-26 15:13:38.193585: LSGate device opened successfully, handle: 2528519287680
2025-08-26 15:13:38.193585: open reader readerType ：22 ret：0
2025-08-26 15:13:38.194580: [[22, 0]]
2025-08-26 15:13:38.194580: changeType:ReaderErrorType.openSuccess
2025-08-26 15:13:38.195576: subThread :ReaderCommand.untilDetected
2025-08-26 15:13:38.195576: commandRsp:ReaderCommand.untilDetected
2025-08-26 15:13:38.195576: subThread :ReaderCommand.startInventory
2025-08-26 15:13:38.195576: commandRsp:ReaderCommand.startInventory
2025-08-26 15:13:38.234448: Rsp : 98YYYNNN00500320250826    1513312.00AOhlsp|AM海恒图书馆|BXYYYYYYYYYYYYYYYY|AN3a40852d-49fd-4df2-a1f9-6e2641a6e91f|AF|AG|AY2AZD523
2025-08-26 15:13:38.292256: 🔄 执行首次轮询...
2025-08-26 15:13:38.292256: 🔄 开始RFID轮询检查...
2025-08-26 15:13:38.293254: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:13:38.293254: ⚠️ tagList为空，场上无标签
2025-08-26 15:13:38.691931: 🔄 开始RFID轮询检查...
2025-08-26 15:13:38.692928: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:13:38.692928: ⚠️ tagList为空，场上无标签
2025-08-26 15:13:38.796583: 🔍 LSGate硬件扫描详情:
2025-08-26 15:13:38.797581:   - 设备句柄: 2528519287680
2025-08-26 15:13:38.797581:   - FetchRecords返回值: 0
2025-08-26 15:13:38.797581:   - 报告数量: 0
2025-08-26 15:13:38.797581: 📊 LSGate扫描结果汇总:
2025-08-26 15:13:38.797581:   - 发现标签数量: 0
2025-08-26 15:13:38.797581:   - 未发现任何RFID标签
2025-08-26 15:13:38.798577: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:13:38.798577: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:13:39.193269: 🔍 验证数据收集状态...
2025-08-26 15:13:39.194266: 📊 当前tagList: 0个标签
2025-08-26 15:13:39.194266: ⚠️ tagList为空，等待RFID硬件扫描到标签
2025-08-26 15:13:39.194266: ✅ 主机持续数据收集已启动，共享池将持续接收RFID数据
2025-08-26 15:13:39.195263: 🔄 轮询机制每500ms检查一次tagList，确保标签不会丢失
2025-08-26 15:13:39.195263: 开始全局初始化共享扫描池服务...
2025-08-26 15:13:39.195263: 共享扫描池已集成现有RFID服务
2025-08-26 15:13:39.196259: 📡 初始化后RFID扫描状态: scanning=false
2025-08-26 15:13:39.196259: 🔄 开始重置已处理条码集合...
2025-08-26 15:13:39.196259: 📊 重置前状态: 大小=0, 内容=[]
2025-08-26 15:13:39.197257: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-26 15:13:39.197257: 🔄 当前场上标签将被重新识别为新标签
2025-08-26 15:13:39.197257: 📊 当前tagList状态: 0个标签
2025-08-26 15:13:39.197257: 🚀 立即触发轮询，加速标签重新发现...
2025-08-26 15:13:39.198253: 🔄 开始RFID轮询检查...
2025-08-26 15:13:39.198253: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:13:39.198253: ⚠️ tagList为空，场上无标签
2025-08-26 15:13:39.199248: 🔄 开始RFID轮询检查...
2025-08-26 15:13:39.199248: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:13:39.199248: ⚠️ tagList为空，场上无标签
2025-08-26 15:13:39.200245: 共享扫描池服务全局初始化完成
2025-08-26 15:13:39.200245: 🚀 初始化新架构服务...
2025-08-26 15:13:39.200245: 🖥️ 主机模式：使用主机集合A服务
2025-08-26 15:13:39.200245: ⏹️ 书籍信息查询服务停止监听
2025-08-26 15:13:39.200245: 🚀 书籍信息查询服务开始监听集合A变化
2025-08-26 15:13:39.200245: ✅ 新架构服务初始化完成
2025-08-26 15:13:39.200245: RFID服务和共享池初始化完成，持续扫描已启动
2025-08-26 15:13:39.201241: 闸机协调器初始化完成
2025-08-26 15:13:39.201241: 🔧 开始初始化主从机扩展（使用持久化配置）...
2025-08-26 15:13:39.201241: 开始初始化主从机扩展...
2025-08-26 15:13:39.201241: 从 seasetting 数据库加载主从机配置成功: channel_1
2025-08-26 15:13:39.201241: 配置详情: 主机模式
2025-08-26 15:13:39.201241: 📡 从 SettingProvider 获取串口配置: COM1 @ 115200
2025-08-26 15:13:39.202238: ✅ 通过 SettingProvider 加载串口配置成功
2025-08-26 15:13:39.202238: 启用主从机扩展: channel_1 (主机)
2025-08-26 15:13:39.202238: ✅ 数据变化通知流已创建
2025-08-26 15:13:39.202238: 共享扫描池已集成现有RFID服务
2025-08-26 15:13:39.202238: 📡 初始化后RFID扫描状态: scanning=false
2025-08-26 15:13:39.202238: 🔄 开始重置已处理条码集合...
2025-08-26 15:13:39.202238: 📊 重置前状态: 大小=0, 内容=[]
2025-08-26 15:13:39.202238: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-26 15:13:39.202238: 🔄 当前场上标签将被重新识别为新标签
2025-08-26 15:13:39.203235: 📊 当前tagList状态: 0个标签
2025-08-26 15:13:39.203235: 🚀 立即触发轮询，加速标签重新发现...
2025-08-26 15:13:39.203235: 🔄 开始RFID轮询检查...
2025-08-26 15:13:39.203235: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:13:39.203235: ⚠️ tagList为空，场上无标签
2025-08-26 15:13:39.203235: 配置为主机模式，监听端口: 8888
2025-08-26 15:13:39.204231: 主机服务器启动成功，监听端口: 8888
2025-08-26 15:13:39.204231: 主机模式配置完成（请求-响应模式）
2025-08-26 15:13:39.204231: [channel_1] 已集成现有GateCoordinator，开始监听事件
2025-08-26 15:13:39.204231: 主从机扩展启用成功
2025-08-26 15:13:39.204231: 主从机扩展初始化完成
2025-08-26 15:13:39.205228: 配置信息: MasterSlaveConfig(channelId: channel_1, isMaster: true, slaveAddress: null, masterAddress: null, port: 8888)
2025-08-26 15:13:39.205228: ✅ 加载到持久化配置: 主机模式, 通道: channel_1
2025-08-26 15:13:39.205228: 主从机扩展初始化完成
2025-08-26 15:13:39.205228: 安全闸机系统初始化完成
2025-08-26 15:13:39.205228: 🔍 LSGate硬件扫描详情:
2025-08-26 15:13:39.205228:   - 设备句柄: 2528519287680
2025-08-26 15:13:39.205228:   - FetchRecords返回值: 0
2025-08-26 15:13:39.205228:   - 报告数量: 0
2025-08-26 15:13:39.206224: 📊 LSGate扫描结果汇总:
2025-08-26 15:13:39.206224:   - 发现标签数量: 0
2025-08-26 15:13:39.206224:   - 未发现任何RFID标签
2025-08-26 15:13:39.206224: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:13:39.206224: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:13:39.215195: 开始初始化MultiAuthManager...
2025-08-26 15:13:39.215195: 多认证管理器状态变更: initializing
2025-08-26 15:13:39.216193: 认证优先级管理器: 开始加载认证方式
2025-08-26 15:13:39.216193: 配置的排序: [读者证认证, 身份证认证, 腾讯E证通认证, 手动输入认证, 人脸识别认证, 社保卡认证, 市民卡认证, 微信二维码认证, 借阅宝认证, 支付宝二维码认证, 芝麻信用码认证, 支付宝二维码认证（阿里信用）, 微信/支付宝认证, 二维码读者认证, IMI身份认证, 电子社保卡认证, 拍照配置, 上海随申码认证, 微信扫码认证, 汇文二维码]
2025-08-26 15:13:39.216193: 可用的认证方式: [读者证认证, 身份证认证, 腾讯E证通认证, 手动输入认证, 人脸识别认证, 社保卡认证, 市民卡认证, 微信二维码认证, 借阅宝认证, 支付宝二维码认证, 芝麻信用码认证, 支付宝二维码认证（阿里信用）, 微信/支付宝认证, 二维码读者认证, IMI身份认证, 电子社保卡认证, 拍照配置, 上海随申码认证, 微信扫码认证, 汇文二维码]
2025-08-26 15:13:39.216193: 认证优先级管理器: 按配置顺序添加 读者证认证 -> 读者证
2025-08-26 15:13:39.216193: 认证优先级管理器: 最终排序结果: 读者证
2025-08-26 15:13:39.216193: 认证优先级管理器: 主要认证方式: 读者证
2025-08-26 15:13:39.216193: 多认证管理器: 从优先级管理器加载的认证方式: 读者证
2025-08-26 15:13:39.217189: 多认证管理器: 当前默认显示方式: 读者证
2025-08-26 15:13:39.217189: 初始化读卡器认证服务
2025-08-26 15:13:39.217189: 读卡器认证服务初始化成功
2025-08-26 15:13:39.217189: 初始化共享读卡器认证服务
2025-08-26 15:13:39.217189: 读者证 认证服务初始化成功
2025-08-26 15:13:39.217189: 认证服务初始化完成，共初始化 1 种认证方式
2025-08-26 15:13:39.217189: 多认证管理器状态变更: idle
2025-08-26 15:13:39.218185: 多认证管理器初始化完成，启用的认证方式: [AuthMethod.readerCard]
2025-08-26 15:13:39.218185: MultiAuthManager初始化完成
2025-08-26 15:13:39.218185: 开始初始化SilencePageViewModel...
2025-08-26 15:13:39.218185: 闸机串口服务已经初始化
2025-08-26 15:13:39.218185: 开始初始化闸机认证服务...
2025-08-26 15:13:39.218185: 闸机认证服务初始化完成，启用认证方式: 人脸识别、读者证、AuthMethod.wechatScanQRCode
2025-08-26 15:13:39.218185: RFID服务已经初始化
2025-08-26 15:13:39.218185: SIP2图书信息服务初始化完成
2025-08-26 15:13:39.219181: 💡 主从机扩展已准备就绪，请通过配置页面手动启用
2025-08-26 15:13:39.219181: 💡 可以通过MasterSlaveConfigPage进行配置
2025-08-26 15:13:39.219181: ✅ 统一事件监听已设置：SilencePageViewModel → GateCoordinator.eventStream
2025-08-26 15:13:39.219181: 串口监听已经启动
2025-08-26 15:13:39.219181: SilencePageViewModel初始化完成
2025-08-26 15:13:39.554087: dispose IndexPage
2025-08-26 15:13:39.555069: IndexPage dispose
2025-08-26 15:13:39.691625: 🔄 开始RFID轮询检查...
2025-08-26 15:13:39.692619: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:13:39.693614: ⚠️ tagList为空，场上无标签
2025-08-26 15:13:39.693614: 🔍 LSGate硬件扫描详情:
2025-08-26 15:13:39.694607:   - 设备句柄: 2528519287680
2025-08-26 15:13:39.694607:   - FetchRecords返回值: 0
2025-08-26 15:13:39.694607:   - 报告数量: 0
2025-08-26 15:13:39.695603: 📊 LSGate扫描结果汇总:
2025-08-26 15:13:39.695603:   - 发现标签数量: 0
2025-08-26 15:13:39.695603:   - 未发现任何RFID标签
2025-08-26 15:13:39.695603: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:13:39.695603: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:13:39.719523: 🔍 主从机模式检测: 启用=true, 主机模式=true
2025-08-26 15:13:39.719523: 🔍 扩展详细状态: {enabled: true, channel_id: channel_1, is_master: true, data_stream_ready: true, data_stream_exists: true, data_stream_closed: false, shared_pool_size: 0, queue_size: 0, comm_connected: false, timestamp: 2025-08-26T15:13:39.719523}
2025-08-26 15:13:39.719523: 🎯 检测到主机模式，无需设置数据监听
2025-08-26 15:13:40.191538: 🔄 开始RFID轮询检查...
2025-08-26 15:13:40.191538: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:13:40.192537: ⚠️ tagList为空，场上无标签
2025-08-26 15:13:40.192537: 🔍 LSGate硬件扫描详情:
2025-08-26 15:13:40.193532:   - 设备句柄: 2528519287680
2025-08-26 15:13:40.193532:   - FetchRecords返回值: 0
2025-08-26 15:13:40.194529:   - 报告数量: 0
2025-08-26 15:13:40.194529: 📊 LSGate扫描结果汇总:
2025-08-26 15:13:40.194529:   - 发现标签数量: 0
2025-08-26 15:13:40.194529:   - 未发现任何RFID标签
2025-08-26 15:13:40.195526: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:13:40.195526: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:13:40.692040: 🔍 LSGate硬件扫描详情:
2025-08-26 15:13:40.693038:   - 设备句柄: 2528519287680
2025-08-26 15:13:40.693038:   - FetchRecords返回值: 0
2025-08-26 15:13:40.694033:   - 报告数量: 0
2025-08-26 15:13:40.694033: 📊 LSGate扫描结果汇总:
2025-08-26 15:13:40.695032:   - 发现标签数量: 0
2025-08-26 15:13:40.695032:   - 未发现任何RFID标签
2025-08-26 15:13:40.696029: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:13:40.696029: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:13:40.697026: 🔄 开始RFID轮询检查...
2025-08-26 15:13:40.697026: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:13:40.698020: ⚠️ tagList为空，场上无标签
2025-08-26 15:13:41.191911: 🔄 开始RFID轮询检查...
2025-08-26 15:13:41.191911: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:13:41.192910: ⚠️ tagList为空，场上无标签
2025-08-26 15:13:41.192910: 🔍 LSGate硬件扫描详情:
2025-08-26 15:13:41.193905:   - 设备句柄: 2528519287680
2025-08-26 15:13:41.193905:   - FetchRecords返回值: 0
2025-08-26 15:13:41.194902:   - 报告数量: 0
2025-08-26 15:13:41.194902: 📊 LSGate扫描结果汇总:
2025-08-26 15:13:41.194902:   - 发现标签数量: 0
2025-08-26 15:13:41.195899:   - 未发现任何RFID标签
2025-08-26 15:13:41.195899: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:13:41.195899: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:13:41.692253: 🔄 开始RFID轮询检查...
2025-08-26 15:13:41.693250: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:13:41.693250: ⚠️ tagList为空，场上无标签
2025-08-26 15:13:41.694247: 🔍 LSGate硬件扫描详情:
2025-08-26 15:13:41.694247:   - 设备句柄: 2528519287680
2025-08-26 15:13:41.695243:   - FetchRecords返回值: 0
2025-08-26 15:13:41.695243:   - 报告数量: 0
2025-08-26 15:13:41.696240: 📊 LSGate扫描结果汇总:
2025-08-26 15:13:41.696240:   - 发现标签数量: 0
2025-08-26 15:13:41.696240:   - 未发现任何RFID标签
2025-08-26 15:13:41.697236: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:13:41.697236: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:13:42.192595: 🔄 开始RFID轮询检查...
2025-08-26 15:13:42.193591: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:13:42.193591: ⚠️ tagList为空，场上无标签
2025-08-26 15:13:42.194590: 🔍 LSGate硬件扫描详情:
2025-08-26 15:13:42.195588:   - 设备句柄: 2528519287680
2025-08-26 15:13:42.196581:   - FetchRecords返回值: 0
2025-08-26 15:13:42.196581:   - 报告数量: 0
2025-08-26 15:13:42.196581: 📊 LSGate扫描结果汇总:
2025-08-26 15:13:42.197578:   - 发现标签数量: 0
2025-08-26 15:13:42.197578:   - 未发现任何RFID标签
2025-08-26 15:13:42.197578: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:13:42.198573: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:13:42.691937: 🔄 开始RFID轮询检查...
2025-08-26 15:13:42.691937: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:13:42.692935: ⚠️ tagList为空，场上无标签
2025-08-26 15:13:42.692935: 🔍 LSGate硬件扫描详情:
2025-08-26 15:13:42.693931:   - 设备句柄: 2528519287680
2025-08-26 15:13:42.693931:   - FetchRecords返回值: 0
2025-08-26 15:13:42.693931:   - 报告数量: 0
2025-08-26 15:13:42.694928: 📊 LSGate扫描结果汇总:
2025-08-26 15:13:42.694928:   - 发现标签数量: 0
2025-08-26 15:13:42.694928:   - 未发现任何RFID标签
2025-08-26 15:13:42.695924: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:13:42.695924: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:13:43.191281: 🔄 开始RFID轮询检查...
2025-08-26 15:13:43.191281: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:13:43.192279: ⚠️ tagList为空，场上无标签
2025-08-26 15:13:43.192279: 🔍 LSGate硬件扫描详情:
2025-08-26 15:13:43.193276:   - 设备句柄: 2528519287680
2025-08-26 15:13:43.193276:   - FetchRecords返回值: 0
2025-08-26 15:13:43.193276:   - 报告数量: 0
2025-08-26 15:13:43.194273: 📊 LSGate扫描结果汇总:
2025-08-26 15:13:43.194273:   - 发现标签数量: 0
2025-08-26 15:13:43.194273:   - 未发现任何RFID标签
2025-08-26 15:13:43.195269: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:13:43.195269: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:13:43.691622: 🔄 开始RFID轮询检查...
2025-08-26 15:13:43.691622: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:13:43.692620: ⚠️ tagList为空，场上无标签
2025-08-26 15:13:43.692620: 🔍 LSGate硬件扫描详情:
2025-08-26 15:13:43.693616:   - 设备句柄: 2528519287680
2025-08-26 15:13:43.693616:   - FetchRecords返回值: 0
2025-08-26 15:13:43.693616:   - 报告数量: 0
2025-08-26 15:13:43.693616: 📊 LSGate扫描结果汇总:
2025-08-26 15:13:43.694613:   - 发现标签数量: 0
2025-08-26 15:13:43.694613:   - 未发现任何RFID标签
2025-08-26 15:13:43.694613: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:13:43.695610: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:13:44.192479: 🔄 开始RFID轮询检查...
2025-08-26 15:13:44.192479: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:13:44.193477: ⚠️ tagList为空，场上无标签
2025-08-26 15:13:44.193477: 🔍 LSGate硬件扫描详情:
2025-08-26 15:13:44.194473:   - 设备句柄: 2528519287680
2025-08-26 15:13:44.194473:   - FetchRecords返回值: 0
2025-08-26 15:13:44.194473:   - 报告数量: 0
2025-08-26 15:13:44.195470: 📊 LSGate扫描结果汇总:
2025-08-26 15:13:44.195470:   - 发现标签数量: 0
2025-08-26 15:13:44.196467:   - 未发现任何RFID标签
2025-08-26 15:13:44.196467: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:13:44.196467: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:13:44.691823: 🔄 开始RFID轮询检查...
2025-08-26 15:13:44.692820: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:13:44.692820: ⚠️ tagList为空，场上无标签
2025-08-26 15:13:44.692820: 🔍 LSGate硬件扫描详情:
2025-08-26 15:13:44.693817:   - 设备句柄: 2528519287680
2025-08-26 15:13:44.693817:   - FetchRecords返回值: 0
2025-08-26 15:13:44.693817:   - 报告数量: 0
2025-08-26 15:13:44.694813: 📊 LSGate扫描结果汇总:
2025-08-26 15:13:44.694813:   - 发现标签数量: 0
2025-08-26 15:13:44.694813:   - 未发现任何RFID标签
2025-08-26 15:13:44.695810: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:13:44.695810: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:13:45.191769: 🔄 开始RFID轮询检查...
2025-08-26 15:13:45.191769: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:13:45.192766: ⚠️ tagList为空，场上无标签
2025-08-26 15:13:45.192766: 🔍 LSGate硬件扫描详情:
2025-08-26 15:13:45.193763:   - 设备句柄: 2528519287680
2025-08-26 15:13:45.193763:   - FetchRecords返回值: 0
2025-08-26 15:13:45.193763:   - 报告数量: 0
2025-08-26 15:13:45.194759: 📊 LSGate扫描结果汇总:
2025-08-26 15:13:45.194759:   - 发现标签数量: 0
2025-08-26 15:13:45.194759:   - 未发现任何RFID标签
2025-08-26 15:13:45.195756: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:13:45.195756: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:13:45.692110: 🔄 开始RFID轮询检查...
2025-08-26 15:13:45.692110: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:13:45.693107: ⚠️ tagList为空，场上无标签
2025-08-26 15:13:45.693107: 🔍 LSGate硬件扫描详情:
2025-08-26 15:13:45.694104:   - 设备句柄: 2528519287680
2025-08-26 15:13:45.694104:   - FetchRecords返回值: 0
2025-08-26 15:13:45.694104:   - 报告数量: 0
2025-08-26 15:13:45.695100: 📊 LSGate扫描结果汇总:
2025-08-26 15:13:45.695100:   - 发现标签数量: 0
2025-08-26 15:13:45.695100:   - 未发现任何RFID标签
2025-08-26 15:13:45.696097: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:13:45.696097: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:13:46.191454: 🔄 开始RFID轮询检查...
2025-08-26 15:13:46.191454: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:13:46.192452: ⚠️ tagList为空，场上无标签
2025-08-26 15:13:46.192452: 🔍 LSGate硬件扫描详情:
2025-08-26 15:13:46.193448:   - 设备句柄: 2528519287680
2025-08-26 15:13:46.193448:   - FetchRecords返回值: 0
2025-08-26 15:13:46.193448:   - 报告数量: 0
2025-08-26 15:13:46.194444: 📊 LSGate扫描结果汇总:
2025-08-26 15:13:46.194444:   - 发现标签数量: 0
2025-08-26 15:13:46.194444:   - 未发现任何RFID标签
2025-08-26 15:13:46.195441: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:13:46.195441: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:13:46.691795: 🔄 开始RFID轮询检查...
2025-08-26 15:13:46.691795: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:13:46.692792: ⚠️ tagList为空，场上无标签
2025-08-26 15:13:46.692792: 🔍 LSGate硬件扫描详情:
2025-08-26 15:13:46.693789:   - 设备句柄: 2528519287680
2025-08-26 15:13:46.693789:   - FetchRecords返回值: 0
2025-08-26 15:13:46.693789:   - 报告数量: 0
2025-08-26 15:13:46.694785: 📊 LSGate扫描结果汇总:
2025-08-26 15:13:46.694785:   - 发现标签数量: 0
2025-08-26 15:13:46.694785:   - 未发现任何RFID标签
2025-08-26 15:13:46.695782: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:13:46.695782: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:13:47.191663: 🔄 开始RFID轮询检查...
2025-08-26 15:13:47.191663: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:13:47.192660: ⚠️ tagList为空，场上无标签
2025-08-26 15:13:47.192660: 🔍 LSGate硬件扫描详情:
2025-08-26 15:13:47.193657:   - 设备句柄: 2528519287680
2025-08-26 15:13:47.193657:   - FetchRecords返回值: 0
2025-08-26 15:13:47.194653:   - 报告数量: 0
2025-08-26 15:13:47.194653: 📊 LSGate扫描结果汇总:
2025-08-26 15:13:47.194653:   - 发现标签数量: 0
2025-08-26 15:13:47.195650:   - 未发现任何RFID标签
2025-08-26 15:13:47.195650: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:13:47.195650: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:13:47.692003: 🔄 开始RFID轮询检查...
2025-08-26 15:13:47.692003: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:13:47.693001: ⚠️ tagList为空，场上无标签
2025-08-26 15:13:47.693001: 🔍 LSGate硬件扫描详情:
2025-08-26 15:13:47.693997:   - 设备句柄: 2528519287680
2025-08-26 15:13:47.693997:   - FetchRecords返回值: 0
2025-08-26 15:13:47.693997:   - 报告数量: 0
2025-08-26 15:13:47.694994: 📊 LSGate扫描结果汇总:
2025-08-26 15:13:47.694994:   - 发现标签数量: 0
2025-08-26 15:13:47.694994:   - 未发现任何RFID标签
2025-08-26 15:13:47.695991: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:13:47.695991: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:13:48.191348: 🔄 开始RFID轮询检查...
2025-08-26 15:13:48.191348: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:13:48.192345: ⚠️ tagList为空，场上无标签
2025-08-26 15:13:48.192345: 🔍 LSGate硬件扫描详情:
2025-08-26 15:13:48.192345:   - 设备句柄: 2528519287680
2025-08-26 15:13:48.193344:   - FetchRecords返回值: 0
2025-08-26 15:13:48.193344:   - 报告数量: 0
2025-08-26 15:13:48.193344: 📊 LSGate扫描结果汇总:
2025-08-26 15:13:48.194338:   - 发现标签数量: 0
2025-08-26 15:13:48.194338:   - 未发现任何RFID标签
2025-08-26 15:13:48.194338: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:13:48.195335: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:13:48.692685: 🔄 开始RFID轮询检查...
2025-08-26 15:13:48.692685: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:13:48.693683: ⚠️ tagList为空，场上无标签
2025-08-26 15:13:48.693683: 🔍 LSGate硬件扫描详情:
2025-08-26 15:13:48.694679:   - 设备句柄: 2528519287680
2025-08-26 15:13:48.694679:   - FetchRecords返回值: 0
2025-08-26 15:13:48.694679:   - 报告数量: 0
2025-08-26 15:13:48.695676: 📊 LSGate扫描结果汇总:
2025-08-26 15:13:48.695676:   - 发现标签数量: 0
2025-08-26 15:13:48.695676:   - 未发现任何RFID标签
2025-08-26 15:13:48.696673: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:13:48.696673: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:13:49.191032: 🔄 开始RFID轮询检查...
2025-08-26 15:13:49.191032: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:13:49.192030: ⚠️ tagList为空，场上无标签
2025-08-26 15:13:49.192030: 🔍 LSGate硬件扫描详情:
2025-08-26 15:13:49.193027:   - 设备句柄: 2528519287680
2025-08-26 15:13:49.193027:   - FetchRecords返回值: 0
2025-08-26 15:13:49.193027:   - 报告数量: 0
2025-08-26 15:13:49.194023: 📊 LSGate扫描结果汇总:
2025-08-26 15:13:49.194023:   - 发现标签数量: 0
2025-08-26 15:13:49.194023:   - 未发现任何RFID标签
2025-08-26 15:13:49.195021: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:13:49.195021: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:13:49.692370: 🔄 开始RFID轮询检查...
2025-08-26 15:13:49.692370: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:13:49.693368: ⚠️ tagList为空，场上无标签
2025-08-26 15:13:49.694365: 🔍 LSGate硬件扫描详情:
2025-08-26 15:13:49.694365:   - 设备句柄: 2528519287680
2025-08-26 15:13:49.694365:   - FetchRecords返回值: 0
2025-08-26 15:13:49.695361:   - 报告数量: 0
2025-08-26 15:13:49.695361: 📊 LSGate扫描结果汇总:
2025-08-26 15:13:49.695361:   - 发现标签数量: 0
2025-08-26 15:13:49.696358:   - 未发现任何RFID标签
2025-08-26 15:13:49.696358: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:13:49.697356: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:13:50.192243: 🔄 开始RFID轮询检查...
2025-08-26 15:13:50.192243: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:13:50.193240: ⚠️ tagList为空，场上无标签
2025-08-26 15:13:50.193240: 🔍 LSGate硬件扫描详情:
2025-08-26 15:13:50.194237:   - 设备句柄: 2528519287680
2025-08-26 15:13:50.194237:   - FetchRecords返回值: 0
2025-08-26 15:13:50.195234:   - 报告数量: 0
2025-08-26 15:13:50.195234: 📊 LSGate扫描结果汇总:
2025-08-26 15:13:50.195234:   - 发现标签数量: 0
2025-08-26 15:13:50.196232:   - 未发现任何RFID标签
2025-08-26 15:13:50.196232: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:13:50.196232: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:13:50.691587: 🔄 开始RFID轮询检查...
2025-08-26 15:13:50.691587: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:13:50.692585: ⚠️ tagList为空，场上无标签
2025-08-26 15:13:50.692585: 🔍 LSGate硬件扫描详情:
2025-08-26 15:13:50.693582:   - 设备句柄: 2528519287680
2025-08-26 15:13:50.693582:   - FetchRecords返回值: 0
2025-08-26 15:13:50.694578:   - 报告数量: 0
2025-08-26 15:13:50.694578: 📊 LSGate扫描结果汇总:
2025-08-26 15:13:50.694578:   - 发现标签数量: 0
2025-08-26 15:13:50.694578:   - 未发现任何RFID标签
2025-08-26 15:13:50.695574: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:13:50.695574: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:13:51.191928: 🔄 开始RFID轮询检查...
2025-08-26 15:13:51.191928: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:13:51.192925: ⚠️ tagList为空，场上无标签
2025-08-26 15:13:51.192925: 🔍 LSGate硬件扫描详情:
2025-08-26 15:13:51.193922:   - 设备句柄: 2528519287680
2025-08-26 15:13:51.193922:   - FetchRecords返回值: 0
2025-08-26 15:13:51.193922:   - 报告数量: 0
2025-08-26 15:13:51.194918: 📊 LSGate扫描结果汇总:
2025-08-26 15:13:51.194918:   - 发现标签数量: 0
2025-08-26 15:13:51.194918:   - 未发现任何RFID标签
2025-08-26 15:13:51.194918: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:13:51.195915: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:13:51.692269: 🔄 开始RFID轮询检查...
2025-08-26 15:13:51.692269: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:13:51.693266: ⚠️ tagList为空，场上无标签
2025-08-26 15:13:51.693266: 🔍 LSGate硬件扫描详情:
2025-08-26 15:13:51.694263:   - 设备句柄: 2528519287680
2025-08-26 15:13:51.694263:   - FetchRecords返回值: 0
2025-08-26 15:13:51.694263:   - 报告数量: 0
2025-08-26 15:13:51.695259: 📊 LSGate扫描结果汇总:
2025-08-26 15:13:51.695259:   - 发现标签数量: 0
2025-08-26 15:13:51.695259:   - 未发现任何RFID标签
2025-08-26 15:13:51.696256: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:13:51.696256: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:13:52.191613: 🔄 开始RFID轮询检查...
2025-08-26 15:13:52.191613: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:13:52.192610: ⚠️ tagList为空，场上无标签
2025-08-26 15:13:52.192610: 🔍 LSGate硬件扫描详情:
2025-08-26 15:13:52.192610:   - 设备句柄: 2528519287680
2025-08-26 15:13:52.193607:   - FetchRecords返回值: 0
2025-08-26 15:13:52.193607:   - 报告数量: 0
2025-08-26 15:13:52.193607: 📊 LSGate扫描结果汇总:
2025-08-26 15:13:52.194603:   - 发现标签数量: 0
2025-08-26 15:13:52.194603:   - 未发现任何RFID标签
2025-08-26 15:13:52.194603: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:13:52.195600: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:13:52.692951: 🔄 开始RFID轮询检查...
2025-08-26 15:13:52.692951: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:13:52.693948: ⚠️ tagList为空，场上无标签
2025-08-26 15:13:52.693948: 🔍 LSGate硬件扫描详情:
2025-08-26 15:13:52.694944:   - 设备句柄: 2528519287680
2025-08-26 15:13:52.694944:   - FetchRecords返回值: 0
2025-08-26 15:13:52.694944:   - 报告数量: 0
2025-08-26 15:13:52.695942: 📊 LSGate扫描结果汇总:
2025-08-26 15:13:52.695942:   - 发现标签数量: 0
2025-08-26 15:13:52.695942:   - 未发现任何RFID标签
2025-08-26 15:13:52.696938: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:13:52.696938: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:13:53.191817: 🔄 开始RFID轮询检查...
2025-08-26 15:13:53.191817: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:13:53.192818: ⚠️ tagList为空，场上无标签
2025-08-26 15:13:53.192818: 🔍 LSGate硬件扫描详情:
2025-08-26 15:13:53.192818:   - 设备句柄: 2528519287680
2025-08-26 15:13:53.193810:   - FetchRecords返回值: 0
2025-08-26 15:13:53.193810:   - 报告数量: 0
2025-08-26 15:13:53.193810: 📊 LSGate扫描结果汇总:
2025-08-26 15:13:53.194807:   - 发现标签数量: 0
2025-08-26 15:13:53.194807:   - 未发现任何RFID标签
2025-08-26 15:13:53.194807: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:13:53.195804: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:13:53.692157: 🔄 开始RFID轮询检查...
2025-08-26 15:13:53.692157: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:13:53.693155: ⚠️ tagList为空，场上无标签
2025-08-26 15:13:53.693155: 🔍 LSGate硬件扫描详情:
2025-08-26 15:13:53.693155:   - 设备句柄: 2528519287680
2025-08-26 15:13:53.694152:   - FetchRecords返回值: 0
2025-08-26 15:13:53.694152:   - 报告数量: 0
2025-08-26 15:13:53.694152: 📊 LSGate扫描结果汇总:
2025-08-26 15:13:53.695147:   - 发现标签数量: 0
2025-08-26 15:13:53.695147:   - 未发现任何RFID标签
2025-08-26 15:13:53.695147: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:13:53.696145: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:13:54.191502: 🔄 开始RFID轮询检查...
2025-08-26 15:13:54.191502: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:13:54.192499: ⚠️ tagList为空，场上无标签
2025-08-26 15:13:54.192499: 🔍 LSGate硬件扫描详情:
2025-08-26 15:13:54.192499:   - 设备句柄: 2528519287680
2025-08-26 15:13:54.193495:   - FetchRecords返回值: 0
2025-08-26 15:13:54.193495:   - 报告数量: 0
2025-08-26 15:13:54.193495: 📊 LSGate扫描结果汇总:
2025-08-26 15:13:54.194492:   - 发现标签数量: 0
2025-08-26 15:13:54.194492:   - 未发现任何RFID标签
2025-08-26 15:13:54.194492: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:13:54.195489: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:13:54.691843: 🔄 开始RFID轮询检查...
2025-08-26 15:13:54.691843: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:13:54.692840: ⚠️ tagList为空，场上无标签
2025-08-26 15:13:54.692840: 🔍 LSGate硬件扫描详情:
2025-08-26 15:13:54.693837:   - 设备句柄: 2528519287680
2025-08-26 15:13:54.693837:   - FetchRecords返回值: 0
2025-08-26 15:13:54.693837:   - 报告数量: 0
2025-08-26 15:13:54.694833: 📊 LSGate扫描结果汇总:
2025-08-26 15:13:54.694833:   - 发现标签数量: 0
2025-08-26 15:13:54.694833:   - 未发现任何RFID标签
2025-08-26 15:13:54.695830: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:13:54.695830: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:13:55.191740: 🔄 开始RFID轮询检查...
2025-08-26 15:13:55.191740: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:13:55.192737: ⚠️ tagList为空，场上无标签
2025-08-26 15:13:55.192737: 🔍 LSGate硬件扫描详情:
2025-08-26 15:13:55.192737:   - 设备句柄: 2528519287680
2025-08-26 15:13:55.193734:   - FetchRecords返回值: 0
2025-08-26 15:13:55.193734:   - 报告数量: 0
2025-08-26 15:13:55.193734: 📊 LSGate扫描结果汇总:
2025-08-26 15:13:55.194730:   - 发现标签数量: 0
2025-08-26 15:13:55.194730:   - 未发现任何RFID标签
2025-08-26 15:13:55.194730: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:13:55.195727: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:13:55.692081: 🔄 开始RFID轮询检查...
2025-08-26 15:13:55.692081: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:13:55.693078: ⚠️ tagList为空，场上无标签
2025-08-26 15:13:55.693078: 🔍 LSGate硬件扫描详情:
2025-08-26 15:13:55.694075:   - 设备句柄: 2528519287680
2025-08-26 15:13:55.694075:   - FetchRecords返回值: 0
2025-08-26 15:13:55.695071:   - 报告数量: 0
2025-08-26 15:13:55.695071: 📊 LSGate扫描结果汇总:
2025-08-26 15:13:55.695071:   - 发现标签数量: 0
2025-08-26 15:13:55.696068:   - 未发现任何RFID标签
2025-08-26 15:13:55.696068: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:13:55.696068: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:13:56.191424: 🔄 开始RFID轮询检查...
2025-08-26 15:13:56.191424: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:13:56.192422: ⚠️ tagList为空，场上无标签
2025-08-26 15:13:56.192422: 🔍 LSGate硬件扫描详情:
2025-08-26 15:13:56.193419:   - 设备句柄: 2528519287680
2025-08-26 15:13:56.193419:   - FetchRecords返回值: 0
2025-08-26 15:13:56.193419:   - 报告数量: 0
2025-08-26 15:13:56.194415: 📊 LSGate扫描结果汇总:
2025-08-26 15:13:56.194415:   - 发现标签数量: 0
2025-08-26 15:13:56.194415:   - 未发现任何RFID标签
2025-08-26 15:13:56.195412: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:13:56.195412: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:13:56.691766: 🔄 开始RFID轮询检查...
2025-08-26 15:13:56.691766: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:13:56.692763: ⚠️ tagList为空，场上无标签
2025-08-26 15:13:56.692763: 🔍 LSGate硬件扫描详情:
2025-08-26 15:13:56.693759:   - 设备句柄: 2528519287680
2025-08-26 15:13:56.693759:   - FetchRecords返回值: 0
2025-08-26 15:13:56.693759:   - 报告数量: 0
2025-08-26 15:13:56.694756: 📊 LSGate扫描结果汇总:
2025-08-26 15:13:56.694756:   - 发现标签数量: 0
2025-08-26 15:13:56.694756:   - 未发现任何RFID标签
2025-08-26 15:13:56.695752: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:13:56.695752: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:13:57.191110: 🔄 开始RFID轮询检查...
2025-08-26 15:13:57.191110: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:13:57.192108: ⚠️ tagList为空，场上无标签
2025-08-26 15:13:57.192108: 🔍 LSGate硬件扫描详情:
2025-08-26 15:13:57.193104:   - 设备句柄: 2528519287680
2025-08-26 15:13:57.193104:   - FetchRecords返回值: 0
2025-08-26 15:13:57.193104:   - 报告数量: 0
2025-08-26 15:13:57.194100: 📊 LSGate扫描结果汇总:
2025-08-26 15:13:57.194100:   - 发现标签数量: 0
2025-08-26 15:13:57.194100:   - 未发现任何RFID标签
2025-08-26 15:13:57.194100: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:13:57.195097: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:13:57.692447: 🔄 开始RFID轮询检查...
2025-08-26 15:13:57.692447: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:13:57.693445: ⚠️ tagList为空，场上无标签
2025-08-26 15:13:57.693445: 🔍 LSGate硬件扫描详情:
2025-08-26 15:13:57.694441:   - 设备句柄: 2528519287680
2025-08-26 15:13:57.694441:   - FetchRecords返回值: 0
2025-08-26 15:13:57.694441:   - 报告数量: 0
2025-08-26 15:13:57.695438: 📊 LSGate扫描结果汇总:
2025-08-26 15:13:57.695438:   - 发现标签数量: 0
2025-08-26 15:13:57.695438:   - 未发现任何RFID标签
2025-08-26 15:13:57.696435: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:13:57.696435: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:13:58.191791: 🔄 开始RFID轮询检查...
2025-08-26 15:13:58.191791: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:13:58.192789: ⚠️ tagList为空，场上无标签
2025-08-26 15:13:58.192789: 🔍 LSGate硬件扫描详情:
2025-08-26 15:13:58.193785:   - 设备句柄: 2528519287680
2025-08-26 15:13:58.193785:   - FetchRecords返回值: 0
2025-08-26 15:13:58.193785:   - 报告数量: 0
2025-08-26 15:13:58.194782: 📊 LSGate扫描结果汇总:
2025-08-26 15:13:58.194782:   - 发现标签数量: 0
2025-08-26 15:13:58.194782:   - 未发现任何RFID标签
2025-08-26 15:13:58.194782: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:13:58.195779: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:13:58.692132: 🔄 开始RFID轮询检查...
2025-08-26 15:13:58.692132: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:13:58.693130: ⚠️ tagList为空，场上无标签
2025-08-26 15:13:58.693130: 🔍 LSGate硬件扫描详情:
2025-08-26 15:13:58.694127:   - 设备句柄: 2528519287680
2025-08-26 15:13:58.694127:   - FetchRecords返回值: 0
2025-08-26 15:13:58.694127:   - 报告数量: 0
2025-08-26 15:13:58.695123: 📊 LSGate扫描结果汇总:
2025-08-26 15:13:58.695123:   - 发现标签数量: 0
2025-08-26 15:13:58.695123:   - 未发现任何RFID标签
2025-08-26 15:13:58.696119: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:13:58.696119: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:13:59.191991: 🔄 开始RFID轮询检查...
2025-08-26 15:13:59.191991: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:13:59.192988: ⚠️ tagList为空，场上无标签
2025-08-26 15:13:59.192988: 🔍 LSGate硬件扫描详情:
2025-08-26 15:13:59.193984:   - 设备句柄: 2528519287680
2025-08-26 15:13:59.193984:   - FetchRecords返回值: 0
2025-08-26 15:13:59.193984:   - 报告数量: 0
2025-08-26 15:13:59.194981: 📊 LSGate扫描结果汇总:
2025-08-26 15:13:59.194981:   - 发现标签数量: 0
2025-08-26 15:13:59.194981:   - 未发现任何RFID标签
2025-08-26 15:13:59.195979: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:13:59.195979: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:13:59.692331: 🔄 开始RFID轮询检查...
2025-08-26 15:13:59.692331: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:13:59.693329: ⚠️ tagList为空，场上无标签
2025-08-26 15:13:59.693329: 🔍 LSGate硬件扫描详情:
2025-08-26 15:13:59.694325:   - 设备句柄: 2528519287680
2025-08-26 15:13:59.694325:   - FetchRecords返回值: 0
2025-08-26 15:13:59.694325:   - 报告数量: 0
2025-08-26 15:13:59.695322: 📊 LSGate扫描结果汇总:
2025-08-26 15:13:59.695322:   - 发现标签数量: 0
2025-08-26 15:13:59.695322:   - 未发现任何RFID标签
2025-08-26 15:13:59.696318: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:13:59.696318: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:00.191230: 🔄 开始RFID轮询检查...
2025-08-26 15:14:00.191230: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:00.192227: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:00.192227: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:00.193224:   - 设备句柄: 2528519287680
2025-08-26 15:14:00.193224:   - FetchRecords返回值: 0
2025-08-26 15:14:00.193224:   - 报告数量: 0
2025-08-26 15:14:00.194220: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:00.194220:   - 发现标签数量: 0
2025-08-26 15:14:00.194220:   - 未发现任何RFID标签
2025-08-26 15:14:00.195217: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:00.195217: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:00.691335: 🔄 开始RFID轮询检查...
2025-08-26 15:14:00.691335: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:00.692333: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:00.692333: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:00.693329:   - 设备句柄: 2528519287680
2025-08-26 15:14:00.693329:   - FetchRecords返回值: 0
2025-08-26 15:14:00.693329:   - 报告数量: 0
2025-08-26 15:14:00.694326: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:00.694326:   - 发现标签数量: 0
2025-08-26 15:14:00.694326:   - 未发现任何RFID标签
2025-08-26 15:14:00.695326: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:00.695326: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:01.191676: 🔄 开始RFID轮询检查...
2025-08-26 15:14:01.191676: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:01.192673: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:01.192673: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:01.193670:   - 设备句柄: 2528519287680
2025-08-26 15:14:01.193670:   - FetchRecords返回值: 0
2025-08-26 15:14:01.194666:   - 报告数量: 0
2025-08-26 15:14:01.194666: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:01.194666:   - 发现标签数量: 0
2025-08-26 15:14:01.195663:   - 未发现任何RFID标签
2025-08-26 15:14:01.195663: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:01.195663: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:01.692016: 🔄 开始RFID轮询检查...
2025-08-26 15:14:01.692016: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:01.693014: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:01.693014: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:01.693014:   - 设备句柄: 2528519287680
2025-08-26 15:14:01.694011:   - FetchRecords返回值: 0
2025-08-26 15:14:01.694011:   - 报告数量: 0
2025-08-26 15:14:01.694011: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:01.695007:   - 发现标签数量: 0
2025-08-26 15:14:01.695007:   - 未发现任何RFID标签
2025-08-26 15:14:01.695007: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:01.696004: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:02.191902: 🔄 开始RFID轮询检查...
2025-08-26 15:14:02.191902: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:02.192901: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:02.192901: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:02.193896:   - 设备句柄: 2528519287680
2025-08-26 15:14:02.193896:   - FetchRecords返回值: 0
2025-08-26 15:14:02.193896:   - 报告数量: 0
2025-08-26 15:14:02.194893: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:02.194893:   - 发现标签数量: 0
2025-08-26 15:14:02.194893:   - 未发现任何RFID标签
2025-08-26 15:14:02.195890: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:02.195890: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:02.692243: 🔄 开始RFID轮询检查...
2025-08-26 15:14:02.692243: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:02.693240: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:02.693240: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:02.694237:   - 设备句柄: 2528519287680
2025-08-26 15:14:02.694237:   - FetchRecords返回值: 0
2025-08-26 15:14:02.694237:   - 报告数量: 0
2025-08-26 15:14:02.694237: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:02.695234:   - 发现标签数量: 0
2025-08-26 15:14:02.695234:   - 未发现任何RFID标签
2025-08-26 15:14:02.695234: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:02.696230: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:03.191587: 🔄 开始RFID轮询检查...
2025-08-26 15:14:03.191587: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:03.192584: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:03.192584: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:03.192584:   - 设备句柄: 2528519287680
2025-08-26 15:14:03.193581:   - FetchRecords返回值: 0
2025-08-26 15:14:03.193581:   - 报告数量: 0
2025-08-26 15:14:03.193581: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:03.194578:   - 发现标签数量: 0
2025-08-26 15:14:03.194578:   - 未发现任何RFID标签
2025-08-26 15:14:03.194578: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:03.195574: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:03.691928: 🔄 开始RFID轮询检查...
2025-08-26 15:14:03.691928: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:03.692926: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:03.692926: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:03.693922:   - 设备句柄: 2528519287680
2025-08-26 15:14:03.693922:   - FetchRecords返回值: 0
2025-08-26 15:14:03.693922:   - 报告数量: 0
2025-08-26 15:14:03.694919: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:03.694919:   - 发现标签数量: 0
2025-08-26 15:14:03.694919:   - 未发现任何RFID标签
2025-08-26 15:14:03.695915: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:03.695915: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:04.191272: 🔄 开始RFID轮询检查...
2025-08-26 15:14:04.191272: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:04.192270: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:04.192270: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:04.193266:   - 设备句柄: 2528519287680
2025-08-26 15:14:04.193266:   - FetchRecords返回值: 0
2025-08-26 15:14:04.193266:   - 报告数量: 0
2025-08-26 15:14:04.194263: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:04.194263:   - 发现标签数量: 0
2025-08-26 15:14:04.194263:   - 未发现任何RFID标签
2025-08-26 15:14:04.195261: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:04.195261: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:04.691613: 🔄 开始RFID轮询检查...
2025-08-26 15:14:04.691613: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:04.692611: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:04.692611: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:04.693607:   - 设备句柄: 2528519287680
2025-08-26 15:14:04.693607:   - FetchRecords返回值: 0
2025-08-26 15:14:04.693607:   - 报告数量: 0
2025-08-26 15:14:04.693607: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:04.694604:   - 发现标签数量: 0
2025-08-26 15:14:04.694604:   - 未发现任何RFID标签
2025-08-26 15:14:04.695600: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:04.695600: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:05.192555: 🔄 开始RFID轮询检查...
2025-08-26 15:14:05.192555: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:05.193553: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:05.193553: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:05.193553:   - 设备句柄: 2528519287680
2025-08-26 15:14:05.194550:   - FetchRecords返回值: 0
2025-08-26 15:14:05.194550:   - 报告数量: 0
2025-08-26 15:14:05.194550: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:05.195546:   - 发现标签数量: 0
2025-08-26 15:14:05.195546:   - 未发现任何RFID标签
2025-08-26 15:14:05.195546: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:05.196543: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:05.691900: 🔄 开始RFID轮询检查...
2025-08-26 15:14:05.691900: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:05.692898: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:05.692898: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:05.693898:   - 设备句柄: 2528519287680
2025-08-26 15:14:05.693898:   - FetchRecords返回值: 0
2025-08-26 15:14:05.694891:   - 报告数量: 0
2025-08-26 15:14:05.694891: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:05.694891:   - 发现标签数量: 0
2025-08-26 15:14:05.695887:   - 未发现任何RFID标签
2025-08-26 15:14:05.695887: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:05.695887: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:06.191244: 🔄 开始RFID轮询检查...
2025-08-26 15:14:06.191244: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:06.192242: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:06.192242: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:06.193238:   - 设备句柄: 2528519287680
2025-08-26 15:14:06.193238:   - FetchRecords返回值: 0
2025-08-26 15:14:06.193238:   - 报告数量: 0
2025-08-26 15:14:06.194234: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:06.194234:   - 发现标签数量: 0
2025-08-26 15:14:06.194234:   - 未发现任何RFID标签
2025-08-26 15:14:06.195231: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:06.195231: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:06.691585: 🔄 开始RFID轮询检查...
2025-08-26 15:14:06.691585: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:06.692583: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:06.692583: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:06.693580:   - 设备句柄: 2528519287680
2025-08-26 15:14:06.693580:   - FetchRecords返回值: 0
2025-08-26 15:14:06.694577:   - 报告数量: 0
2025-08-26 15:14:06.694577: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:06.694577:   - 发现标签数量: 0
2025-08-26 15:14:06.695572:   - 未发现任何RFID标签
2025-08-26 15:14:06.695572: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:06.695572: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:07.191926: 🔄 开始RFID轮询检查...
2025-08-26 15:14:07.191926: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:07.192923: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:07.192923: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:07.193920:   - 设备句柄: 2528519287680
2025-08-26 15:14:07.193920:   - FetchRecords返回值: 0
2025-08-26 15:14:07.193920:   - 报告数量: 0
2025-08-26 15:14:07.194917: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:07.194917:   - 发现标签数量: 0
2025-08-26 15:14:07.194917:   - 未发现任何RFID标签
2025-08-26 15:14:07.195914: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:07.195914: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:07.692266: 🔄 开始RFID轮询检查...
2025-08-26 15:14:07.692266: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:07.693264: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:07.693264: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:07.694261:   - 设备句柄: 2528519287680
2025-08-26 15:14:07.694261:   - FetchRecords返回值: 0
2025-08-26 15:14:07.694261:   - 报告数量: 0
2025-08-26 15:14:07.695258: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:07.695258:   - 发现标签数量: 0
2025-08-26 15:14:07.695258:   - 未发现任何RFID标签
2025-08-26 15:14:07.696257: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:07.696257: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:08.192137: 🔄 开始RFID轮询检查...
2025-08-26 15:14:08.192137: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:08.193135: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:08.193135: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:08.194131:   - 设备句柄: 2528519287680
2025-08-26 15:14:08.194131:   - FetchRecords返回值: 0
2025-08-26 15:14:08.194131:   - 报告数量: 0
2025-08-26 15:14:08.195128: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:08.195128:   - 发现标签数量: 0
2025-08-26 15:14:08.195128:   - 未发现任何RFID标签
2025-08-26 15:14:08.196126: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:08.196126: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:08.691481: 🔄 开始RFID轮询检查...
2025-08-26 15:14:08.691481: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:08.692479: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:08.692479: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:08.693475:   - 设备句柄: 2528519287680
2025-08-26 15:14:08.693475:   - FetchRecords返回值: 0
2025-08-26 15:14:08.693475:   - 报告数量: 0
2025-08-26 15:14:08.694472: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:08.694472:   - 发现标签数量: 0
2025-08-26 15:14:08.694472:   - 未发现任何RFID标签
2025-08-26 15:14:08.694472: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:08.695469: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:09.191822: 🔄 开始RFID轮询检查...
2025-08-26 15:14:09.191822: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:09.192820: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:09.192820: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:09.192820:   - 设备句柄: 2528519287680
2025-08-26 15:14:09.193816:   - FetchRecords返回值: 0
2025-08-26 15:14:09.193816:   - 报告数量: 0
2025-08-26 15:14:09.193816: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:09.194813:   - 发现标签数量: 0
2025-08-26 15:14:09.194813:   - 未发现任何RFID标签
2025-08-26 15:14:09.194813: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:09.195810: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:09.692163: 🔄 开始RFID轮询检查...
2025-08-26 15:14:09.692163: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:09.693161: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:09.693161: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:09.694157:   - 设备句柄: 2528519287680
2025-08-26 15:14:09.694157:   - FetchRecords返回值: 0
2025-08-26 15:14:09.694157:   - 报告数量: 0
2025-08-26 15:14:09.695155: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:09.695155:   - 发现标签数量: 0
2025-08-26 15:14:09.695155:   - 未发现任何RFID标签
2025-08-26 15:14:09.696151: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:09.696151: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:10.191507: 🔄 开始RFID轮询检查...
2025-08-26 15:14:10.191507: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:10.192505: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:10.192505: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:10.193501:   - 设备句柄: 2528519287680
2025-08-26 15:14:10.193501:   - FetchRecords返回值: 0
2025-08-26 15:14:10.193501:   - 报告数量: 0
2025-08-26 15:14:10.194498: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:10.194498:   - 发现标签数量: 0
2025-08-26 15:14:10.194498:   - 未发现任何RFID标签
2025-08-26 15:14:10.195496: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:10.195496: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:10.691848: 🔄 开始RFID轮询检查...
2025-08-26 15:14:10.691848: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:10.692846: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:10.692846: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:10.693842:   - 设备句柄: 2528519287680
2025-08-26 15:14:10.693842:   - FetchRecords返回值: 0
2025-08-26 15:14:10.693842:   - 报告数量: 0
2025-08-26 15:14:10.694839: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:10.694839:   - 发现标签数量: 0
2025-08-26 15:14:10.694839:   - 未发现任何RFID标签
2025-08-26 15:14:10.694839: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:10.695835: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:11.191706: 🔄 开始RFID轮询检查...
2025-08-26 15:14:11.191706: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:11.192704: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:11.192704: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:11.192704:   - 设备句柄: 2528519287680
2025-08-26 15:14:11.193701:   - FetchRecords返回值: 0
2025-08-26 15:14:11.193701:   - 报告数量: 0
2025-08-26 15:14:11.193701: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:11.194697:   - 发现标签数量: 0
2025-08-26 15:14:11.194697:   - 未发现任何RFID标签
2025-08-26 15:14:11.194697: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:11.195694: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:11.692047: 🔄 开始RFID轮询检查...
2025-08-26 15:14:11.693046: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:11.693046: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:11.693046: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:11.694041:   - 设备句柄: 2528519287680
2025-08-26 15:14:11.694041:   - FetchRecords返回值: 0
2025-08-26 15:14:11.694041:   - 报告数量: 0
2025-08-26 15:14:11.695041: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:11.695041:   - 发现标签数量: 0
2025-08-26 15:14:11.695041:   - 未发现任何RFID标签
2025-08-26 15:14:11.696035: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:11.696035: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:12.191392: 🔄 开始RFID轮询检查...
2025-08-26 15:14:12.191392: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:12.192388: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:12.193385: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:12.193385:   - 设备句柄: 2528519287680
2025-08-26 15:14:12.193385:   - FetchRecords返回值: 0
2025-08-26 15:14:12.194383:   - 报告数量: 0
2025-08-26 15:14:12.194383: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:12.194383:   - 发现标签数量: 0
2025-08-26 15:14:12.195379:   - 未发现任何RFID标签
2025-08-26 15:14:12.195379: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:12.196375: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:12.691732: 🔄 开始RFID轮询检查...
2025-08-26 15:14:12.691732: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:12.692730: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:12.692730: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:12.693727:   - 设备句柄: 2528519287680
2025-08-26 15:14:12.693727:   - FetchRecords返回值: 0
2025-08-26 15:14:12.693727:   - 报告数量: 0
2025-08-26 15:14:12.694723: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:12.694723:   - 发现标签数量: 0
2025-08-26 15:14:12.694723:   - 未发现任何RFID标签
2025-08-26 15:14:12.695720: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:12.695720: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:13.191077: 🔄 开始RFID轮询检查...
2025-08-26 15:14:13.191077: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:13.192074: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:13.192074: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:13.193070:   - 设备句柄: 2528519287680
2025-08-26 15:14:13.193070:   - FetchRecords返回值: 0
2025-08-26 15:14:13.193070:   - 报告数量: 0
2025-08-26 15:14:13.194067: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:13.194067:   - 发现标签数量: 0
2025-08-26 15:14:13.194067:   - 未发现任何RFID标签
2025-08-26 15:14:13.194067: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:13.195064: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:13.692414: 🔄 开始RFID轮询检查...
2025-08-26 15:14:13.692414: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:13.693412: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:13.694408: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:13.694408:   - 设备句柄: 2528519287680
2025-08-26 15:14:13.694408:   - FetchRecords返回值: 0
2025-08-26 15:14:13.695406:   - 报告数量: 0
2025-08-26 15:14:13.695406: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:13.695406:   - 发现标签数量: 0
2025-08-26 15:14:13.696402:   - 未发现任何RFID标签
2025-08-26 15:14:13.696402: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:13.697400: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:14.192290: 🔄 开始RFID轮询检查...
2025-08-26 15:14:14.192290: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:14.193287: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:14.193287: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:14.194283:   - 设备句柄: 2528519287680
2025-08-26 15:14:14.194283:   - FetchRecords返回值: 0
2025-08-26 15:14:14.194283:   - 报告数量: 0
2025-08-26 15:14:14.194283: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:14.195280:   - 发现标签数量: 0
2025-08-26 15:14:14.195280:   - 未发现任何RFID标签
2025-08-26 15:14:14.195280: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:14.196277: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:14.691634: 🔄 开始RFID轮询检查...
2025-08-26 15:14:14.691634: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:14.692631: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:14.692631: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:14.693628:   - 设备句柄: 2528519287680
2025-08-26 15:14:14.693628:   - FetchRecords返回值: 0
2025-08-26 15:14:14.693628:   - 报告数量: 0
2025-08-26 15:14:14.694624: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:14.694624:   - 发现标签数量: 0
2025-08-26 15:14:14.694624:   - 未发现任何RFID标签
2025-08-26 15:14:14.694624: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:14.695621: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:15.191518: 🔄 开始RFID轮询检查...
2025-08-26 15:14:15.191518: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:15.191518: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:15.192516: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:15.192516:   - 设备句柄: 2528519287680
2025-08-26 15:14:15.193512:   - FetchRecords返回值: 0
2025-08-26 15:14:15.193512:   - 报告数量: 0
2025-08-26 15:14:15.193512: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:15.194509:   - 发现标签数量: 0
2025-08-26 15:14:15.194509:   - 未发现任何RFID标签
2025-08-26 15:14:15.194509: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:15.195506: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:15.691859: 🔄 开始RFID轮询检查...
2025-08-26 15:14:15.691859: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:15.692856: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:15.692856: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:15.693853:   - 设备句柄: 2528519287680
2025-08-26 15:14:15.693853:   - FetchRecords返回值: 0
2025-08-26 15:14:15.693853:   - 报告数量: 0
2025-08-26 15:14:15.693853: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:15.694850:   - 发现标签数量: 0
2025-08-26 15:14:15.694850:   - 未发现任何RFID标签
2025-08-26 15:14:15.694850: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:15.695846: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:16.191203: 🔄 开始RFID轮询检查...
2025-08-26 15:14:16.191203: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:16.192201: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:16.192201: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:16.193197:   - 设备句柄: 2528519287680
2025-08-26 15:14:16.193197:   - FetchRecords返回值: 0
2025-08-26 15:14:16.193197:   - 报告数量: 0
2025-08-26 15:14:16.194194: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:16.194194:   - 发现标签数量: 0
2025-08-26 15:14:16.194194:   - 未发现任何RFID标签
2025-08-26 15:14:16.195192: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:16.195192: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:16.691544: 🔄 开始RFID轮询检查...
2025-08-26 15:14:16.691544: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:16.692542: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:16.692542: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:16.693539:   - 设备句柄: 2528519287680
2025-08-26 15:14:16.693539:   - FetchRecords返回值: 0
2025-08-26 15:14:16.693539:   - 报告数量: 0
2025-08-26 15:14:16.694535: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:16.694535:   - 发现标签数量: 0
2025-08-26 15:14:16.694535:   - 未发现任何RFID标签
2025-08-26 15:14:16.695532: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:16.695532: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:17.191970: 🔄 开始RFID轮询检查...
2025-08-26 15:14:17.191970: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:17.192967: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:17.192967: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:17.192967:   - 设备句柄: 2528519287680
2025-08-26 15:14:17.193964:   - FetchRecords返回值: 0
2025-08-26 15:14:17.193964:   - 报告数量: 0
2025-08-26 15:14:17.193964: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:17.194960:   - 发现标签数量: 0
2025-08-26 15:14:17.194960:   - 未发现任何RFID标签
2025-08-26 15:14:17.194960: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:17.195957: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:17.692310: 🔄 开始RFID轮询检查...
2025-08-26 15:14:17.692310: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:17.693308: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:17.693308: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:17.694305:   - 设备句柄: 2528519287680
2025-08-26 15:14:17.694305:   - FetchRecords返回值: 0
2025-08-26 15:14:17.694305:   - 报告数量: 0
2025-08-26 15:14:17.695302: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:17.695302:   - 发现标签数量: 0
2025-08-26 15:14:17.695302:   - 未发现任何RFID标签
2025-08-26 15:14:17.696299: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:17.696299: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:18.191655: 🔄 开始RFID轮询检查...
2025-08-26 15:14:18.192652: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:18.192652: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:18.192652: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:18.193648:   - 设备句柄: 2528519287680
2025-08-26 15:14:18.193648:   - FetchRecords返回值: 0
2025-08-26 15:14:18.193648:   - 报告数量: 0
2025-08-26 15:14:18.194645: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:18.194645:   - 发现标签数量: 0
2025-08-26 15:14:18.194645:   - 未发现任何RFID标签
2025-08-26 15:14:18.195642: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:18.195642: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:18.691996: 🔄 开始RFID轮询检查...
2025-08-26 15:14:18.691996: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:18.692993: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:18.692993: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:18.693990:   - 设备句柄: 2528519287680
2025-08-26 15:14:18.693990:   - FetchRecords返回值: 0
2025-08-26 15:14:18.693990:   - 报告数量: 0
2025-08-26 15:14:18.694987: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:18.694987:   - 发现标签数量: 0
2025-08-26 15:14:18.694987:   - 未发现任何RFID标签
2025-08-26 15:14:18.695984: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:18.695984: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:19.191340: 🔄 开始RFID轮询检查...
2025-08-26 15:14:19.191340: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:19.192338: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:19.192338: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:19.193334:   - 设备句柄: 2528519287680
2025-08-26 15:14:19.193334:   - FetchRecords返回值: 0
2025-08-26 15:14:19.193334:   - 报告数量: 0
2025-08-26 15:14:19.194331: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:19.194331:   - 发现标签数量: 0
2025-08-26 15:14:19.194331:   - 未发现任何RFID标签
2025-08-26 15:14:19.195328: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:19.195328: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:19.691681: 🔄 开始RFID轮询检查...
2025-08-26 15:14:19.692678: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:19.692678: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:19.693675: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:19.693675:   - 设备句柄: 2528519287680
2025-08-26 15:14:19.693675:   - FetchRecords返回值: 0
2025-08-26 15:14:19.694671:   - 报告数量: 0
2025-08-26 15:14:19.694671: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:19.694671:   - 发现标签数量: 0
2025-08-26 15:14:19.695669:   - 未发现任何RFID标签
2025-08-26 15:14:19.695669: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:19.695669: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:20.191131: 🔄 开始RFID轮询检查...
2025-08-26 15:14:20.191131: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:20.192129: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:20.192129: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:20.193126:   - 设备句柄: 2528519287680
2025-08-26 15:14:20.193126:   - FetchRecords返回值: 0
2025-08-26 15:14:20.193126:   - 报告数量: 0
2025-08-26 15:14:20.194122: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:20.194122:   - 发现标签数量: 0
2025-08-26 15:14:20.194122:   - 未发现任何RFID标签
2025-08-26 15:14:20.194122: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:20.195119: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:20.691473: 🔄 开始RFID轮询检查...
2025-08-26 15:14:20.691473: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:20.692470: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:20.692470: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:20.693467:   - 设备句柄: 2528519287680
2025-08-26 15:14:20.693467:   - FetchRecords返回值: 0
2025-08-26 15:14:20.693467:   - 报告数量: 0
2025-08-26 15:14:20.694463: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:20.694463:   - 发现标签数量: 0
2025-08-26 15:14:20.694463:   - 未发现任何RFID标签
2025-08-26 15:14:20.695460: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:20.695460: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:21.191813: 🔄 开始RFID轮询检查...
2025-08-26 15:14:21.191813: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:21.192811: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:21.192811: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:21.193808:   - 设备句柄: 2528519287680
2025-08-26 15:14:21.193808:   - FetchRecords返回值: 0
2025-08-26 15:14:21.193808:   - 报告数量: 0
2025-08-26 15:14:21.194804: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:21.194804:   - 发现标签数量: 0
2025-08-26 15:14:21.194804:   - 未发现任何RFID标签
2025-08-26 15:14:21.195802: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:21.195802: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:21.692154: 🔄 开始RFID轮询检查...
2025-08-26 15:14:21.692154: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:21.693152: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:21.693152: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:21.694149:   - 设备句柄: 2528519287680
2025-08-26 15:14:21.694149:   - FetchRecords返回值: 0
2025-08-26 15:14:21.695145:   - 报告数量: 0
2025-08-26 15:14:21.695145: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:21.695145:   - 发现标签数量: 0
2025-08-26 15:14:21.696142:   - 未发现任何RFID标签
2025-08-26 15:14:21.696142: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:21.696142: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:22.191498: 🔄 开始RFID轮询检查...
2025-08-26 15:14:22.191498: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:22.192496: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:22.192496: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:22.193492:   - 设备句柄: 2528519287680
2025-08-26 15:14:22.193492:   - FetchRecords返回值: 0
2025-08-26 15:14:22.193492:   - 报告数量: 0
2025-08-26 15:14:22.194489: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:22.194489:   - 发现标签数量: 0
2025-08-26 15:14:22.194489:   - 未发现任何RFID标签
2025-08-26 15:14:22.194489: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:22.195486: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:22.691839: 🔄 开始RFID轮询检查...
2025-08-26 15:14:22.691839: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:22.692837: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:22.692837: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:22.693833:   - 设备句柄: 2528519287680
2025-08-26 15:14:22.693833:   - FetchRecords返回值: 0
2025-08-26 15:14:22.693833:   - 报告数量: 0
2025-08-26 15:14:22.694830: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:22.694830:   - 发现标签数量: 0
2025-08-26 15:14:22.694830:   - 未发现任何RFID标签
2025-08-26 15:14:22.694830: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:22.695827: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:23.191704: 🔄 开始RFID轮询检查...
2025-08-26 15:14:23.191704: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:23.192701: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:23.192701: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:23.192701:   - 设备句柄: 2528519287680
2025-08-26 15:14:23.193698:   - FetchRecords返回值: 0
2025-08-26 15:14:23.193698:   - 报告数量: 0
2025-08-26 15:14:23.193698: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:23.194695:   - 发现标签数量: 0
2025-08-26 15:14:23.194695:   - 未发现任何RFID标签
2025-08-26 15:14:23.194695: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:23.195692: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:23.692045: 🔄 开始RFID轮询检查...
2025-08-26 15:14:23.692045: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:23.693042: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:23.693042: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:23.694039:   - 设备句柄: 2528519287680
2025-08-26 15:14:23.694039:   - FetchRecords返回值: 0
2025-08-26 15:14:23.694039:   - 报告数量: 0
2025-08-26 15:14:23.695036: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:23.695036:   - 发现标签数量: 0
2025-08-26 15:14:23.695036:   - 未发现任何RFID标签
2025-08-26 15:14:23.696038: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:23.696038: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:24.191389: 🔄 开始RFID轮询检查...
2025-08-26 15:14:24.191389: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:24.192387: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:24.192387: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:24.193383:   - 设备句柄: 2528519287680
2025-08-26 15:14:24.193383:   - FetchRecords返回值: 0
2025-08-26 15:14:24.193383:   - 报告数量: 0
2025-08-26 15:14:24.194379: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:24.194379:   - 发现标签数量: 0
2025-08-26 15:14:24.194379:   - 未发现任何RFID标签
2025-08-26 15:14:24.195376: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:24.195376: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:24.691730: 🔄 开始RFID轮询检查...
2025-08-26 15:14:24.691730: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:24.692727: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:24.692727: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:24.693724:   - 设备句柄: 2528519287680
2025-08-26 15:14:24.693724:   - FetchRecords返回值: 0
2025-08-26 15:14:24.693724:   - 报告数量: 0
2025-08-26 15:14:24.693724: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:24.694720:   - 发现标签数量: 0
2025-08-26 15:14:24.694720:   - 未发现任何RFID标签
2025-08-26 15:14:24.694720: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:24.695717: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:25.191616: 🔄 开始RFID轮询检查...
2025-08-26 15:14:25.191616: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:25.192613: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:25.192613: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:25.193610:   - 设备句柄: 2528519287680
2025-08-26 15:14:25.193610:   - FetchRecords返回值: 0
2025-08-26 15:14:25.193610:   - 报告数量: 0
2025-08-26 15:14:25.194607: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:25.194607:   - 发现标签数量: 0
2025-08-26 15:14:25.194607:   - 未发现任何RFID标签
2025-08-26 15:14:25.195604: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:25.195604: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:25.691957: 🔄 开始RFID轮询检查...
2025-08-26 15:14:25.691957: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:25.692954: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:25.692954: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:25.693954:   - 设备句柄: 2528519287680
2025-08-26 15:14:25.693954:   - FetchRecords返回值: 0
2025-08-26 15:14:25.693954:   - 报告数量: 0
2025-08-26 15:14:25.694948: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:25.694948:   - 发现标签数量: 0
2025-08-26 15:14:25.695944:   - 未发现任何RFID标签
2025-08-26 15:14:25.695944: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:25.695944: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:26.191834: 🔄 开始RFID轮询检查...
2025-08-26 15:14:26.192831: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:26.192831: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:26.193828: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:26.193828:   - 设备句柄: 2528519287680
2025-08-26 15:14:26.193828:   - FetchRecords返回值: 0
2025-08-26 15:14:26.194825:   - 报告数量: 0
2025-08-26 15:14:26.194825: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:26.194825:   - 发现标签数量: 0
2025-08-26 15:14:26.195821:   - 未发现任何RFID标签
2025-08-26 15:14:26.195821: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:26.195821: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:26.692174: 🔄 开始RFID轮询检查...
2025-08-26 15:14:26.692174: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:26.693172: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:26.693172: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:26.693172:   - 设备句柄: 2528519287680
2025-08-26 15:14:26.694168:   - FetchRecords返回值: 0
2025-08-26 15:14:26.694168:   - 报告数量: 0
2025-08-26 15:14:26.694168: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:26.695165:   - 发现标签数量: 0
2025-08-26 15:14:26.695165:   - 未发现任何RFID标签
2025-08-26 15:14:26.695165: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:26.696161: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:27.191519: 🔄 开始RFID轮询检查...
2025-08-26 15:14:27.191519: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:27.192515: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:27.192515: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:27.192515:   - 设备句柄: 2528519287680
2025-08-26 15:14:27.193513:   - FetchRecords返回值: 0
2025-08-26 15:14:27.193513:   - 报告数量: 0
2025-08-26 15:14:27.193513: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:27.194509:   - 发现标签数量: 0
2025-08-26 15:14:27.194509:   - 未发现任何RFID标签
2025-08-26 15:14:27.194509: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:27.195506: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:27.691859: 🔄 开始RFID轮询检查...
2025-08-26 15:14:27.691859: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:27.692857: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:27.692857: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:27.693853:   - 设备句柄: 2528519287680
2025-08-26 15:14:27.693853:   - FetchRecords返回值: 0
2025-08-26 15:14:27.693853:   - 报告数量: 0
2025-08-26 15:14:27.694850: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:27.694850:   - 发现标签数量: 0
2025-08-26 15:14:27.694850:   - 未发现任何RFID标签
2025-08-26 15:14:27.695847: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:27.695847: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:28.191204: 🔄 开始RFID轮询检查...
2025-08-26 15:14:28.191204: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:28.192202: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:28.192202: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:28.193198:   - 设备句柄: 2528519287680
2025-08-26 15:14:28.193198:   - FetchRecords返回值: 0
2025-08-26 15:14:28.193198:   - 报告数量: 0
2025-08-26 15:14:28.194195: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:28.194195:   - 发现标签数量: 0
2025-08-26 15:14:28.194195:   - 未发现任何RFID标签
2025-08-26 15:14:28.195192: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:28.195192: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:28.474265: 接收到数据: aa 00 c8 80 00 00 27 82
2025-08-26 15:14:28.474265: 🔍 接收到串口数据: aa 00 c8 80 00 00 27 82
2025-08-26 15:14:28.475264: 🔍 数据长度: 8 字节
2025-08-26 15:14:28.475264: 🔍 预定义命令列表:
2025-08-26 15:14:28.475264:   GateCommand.enterStart: aa 00 64 80 00 00 06 d2
2025-08-26 15:14:28.475264:   GateCommand.enterEnd: aa 00 65 80 00 00 07 2e
2025-08-26 15:14:28.476258:   GateCommand.exitStart: aa 00 c8 80 00 00 27 82
2025-08-26 15:14:28.476258:   GateCommand.exitEnd: aa 00 c9 80 00 00 26 7e
2025-08-26 15:14:28.476258:   GateCommand.reachPosition: aa 00 0a 80 00 00
2025-08-26 15:14:28.476258:   GateCommand.enterOpen: aa 00 02 01 00 00 48 72
2025-08-26 15:14:28.477255:   GateCommand.exitOpen: aa 00 01 01 00 00 48 36
2025-08-26 15:14:28.477255:   GateCommand.failSignal: aa 00 02 01 00 00 48 72
2025-08-26 15:14:28.477255:   GateCommand.tailgating: aa 00 0f 80 00 00
2025-08-26 15:14:28.477255:   GateCommand.doorHasPerson: aa 00 0b 80 00 00
2025-08-26 15:14:28.478251: ✅ 解析到闸机命令: GateCommand.exitStart
2025-08-26 15:14:28.478251: 解析到闸机命令: exit_start (出馆开始)
2025-08-26 15:14:28.478251: 收到闸机命令: exit_start (出馆开始)
2025-08-26 15:14:28.478251: 🚪 收到出馆开始命令，等待出馆到位信号...
2025-08-26 15:14:28.478251: 闸机状态变更: GateState.idle -> GateState.exitStarted
2025-08-26 15:14:28.479247: 闸机状态更新: GateState.idle -> GateState.exitStarted
2025-08-26 15:14:28.479247: 📊 流程状态：出馆流程已开始，等待到位信号
2025-08-26 15:14:28.479247: [channel_1] 收到闸机事件: state_changed
2025-08-26 15:14:28.480246: 📨 收到GateCoordinator事件: state_changed
2025-08-26 15:14:28.480246: 闸机状态变更: GateState.exitStarted
2025-08-26 15:14:28.480246: 🎨 处理状态变更UI: exitStarted
2025-08-26 15:14:28.480246: 未处理的状态变更UI: exitStarted
2025-08-26 15:14:28.480246: [channel_1] 收到闸机事件: exit_start
2025-08-26 15:14:28.481242: [channel_1] 主从机扩展：处理出馆开始（请求-响应模式）
2025-08-26 15:14:28.481242: 扫描结果已清空
2025-08-26 15:14:28.481242: 🧹 [channel_1] 已清空RFID服务扫描结果（页面计数重置）
2025-08-26 15:14:28.481242: 清空共享扫描池和RFID缓冲区（保持HWTagProvider标签列表）...
2025-08-26 15:14:28.481242: 🧹 开始清空共享扫描池...
2025-08-26 15:14:28.481242: 📊 清空前状态: 大小=0, 内容=[]
2025-08-26 15:14:28.481242: 🔄 重置RFID去重集合...
2025-08-26 15:14:28.481242: 🔄 开始重置已处理条码集合...
2025-08-26 15:14:28.481242: 📊 重置前状态: 大小=0, 内容=[]
2025-08-26 15:14:28.481242: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-26 15:14:28.482238: 🔄 当前场上标签将被重新识别为新标签
2025-08-26 15:14:28.482238: 📊 当前tagList状态: 0个标签
2025-08-26 15:14:28.482238: 🚀 立即触发轮询，加速标签重新发现...
2025-08-26 15:14:28.482238: 🔄 开始RFID轮询检查...
2025-08-26 15:14:28.482238: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:28.482238: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:28.482238: ✅ 已重置RFID去重集合，现有标签将被重新识别
2025-08-26 15:14:28.482238: 📡 RFID扫描状态（清空后）: isScanning=false
2025-08-26 15:14:28.482238: ⚠️ RFID未在扫描状态，尝试启动数据收集以恢复轮询...
2025-08-26 15:14:28.482238: 开始RFID数据收集...
2025-08-26 15:14:28.483234: 🔄 RFID数据收集已在进行中，重置防重复机制
2025-08-26 15:14:28.483234: ✅ 已处理条码列表已清空，轮询将重新发现标签
2025-08-26 15:14:28.483234: ✅ 共享扫描池已清空: 0 -> 0
2025-08-26 15:14:28.483234: 📡 清空通知已发送，等待RFID重新检测标签...
2025-08-26 15:14:28.483234: 清空RFID扫描缓冲区...
2025-08-26 15:14:28.483234: 🧹 已清空HWTagProvider: 0 -> 0个标签
2025-08-26 15:14:28.483234: 🔧 主机模式：清空软件缓冲区，保持硬件持续扫描
2025-08-26 15:14:28.483234: 🔄 开始重置已处理条码集合...
2025-08-26 15:14:28.483234: 📊 重置前状态: 大小=0, 内容=[]
2025-08-26 15:14:28.483234: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-26 15:14:28.484231: 🔄 当前场上标签将被重新识别为新标签
2025-08-26 15:14:28.484231: 📊 当前tagList状态: 0个标签
2025-08-26 15:14:28.484231: 🚀 立即触发轮询，加速标签重新发现...
2025-08-26 15:14:28.484231: 🔄 开始RFID轮询检查...
2025-08-26 15:14:28.484231: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:28.484231: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:28.484231: ✅ 软件缓冲区已清空，硬件扫描保持运行
2025-08-26 15:14:28.484231: 📨 收到GateCoordinator事件: exit_start
2025-08-26 15:14:28.485228: 页面状态变更: SilencePageState.waitingExit
2025-08-26 15:14:28.485228: RFID数据收集已启动
2025-08-26 15:14:28.485228: ✅ 已启动RFID数据收集（恢复轮询）
2025-08-26 15:14:28.485228: 🔄 开始重置已处理条码集合...
2025-08-26 15:14:28.485228: 📊 重置前状态: 大小=0, 内容=[]
2025-08-26 15:14:28.485228: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-26 15:14:28.485228: 🔄 当前场上标签将被重新识别为新标签
2025-08-26 15:14:28.485228: 📊 当前tagList状态: 0个标签
2025-08-26 15:14:28.485228: 🚀 立即触发轮询，加速标签重新发现...
2025-08-26 15:14:28.485228: 🔄 开始RFID轮询检查...
2025-08-26 15:14:28.486224: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:28.486224: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:28.486224: 共享扫描池和RFID缓冲区已清空，已处理条码集合已重置
2025-08-26 15:14:28.486224: 🧹 [channel_1] 主机清空列表1和RFID缓冲区: 清除0个条码
2025-08-26 15:14:28.691544: 🔄 开始RFID轮询检查...
2025-08-26 15:14:28.691544: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:28.691544: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:28.692541: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:28.692541:   - 设备句柄: 2528519287680
2025-08-26 15:14:28.692541:   - FetchRecords返回值: 0
2025-08-26 15:14:28.692541:   - 报告数量: 0
2025-08-26 15:14:28.692541: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:28.693537:   - 发现标签数量: 0
2025-08-26 15:14:28.693537:   - 未发现任何RFID标签
2025-08-26 15:14:28.693537: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:28.693537: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:29.192494: 🔄 开始RFID轮询检查...
2025-08-26 15:14:29.193492: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:29.193492: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:29.194491: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:29.194491:   - 设备句柄: 2528519287680
2025-08-26 15:14:29.194491:   - FetchRecords返回值: 0
2025-08-26 15:14:29.194491:   - 报告数量: 0
2025-08-26 15:14:29.195485: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:29.195485:   - 发现标签数量: 0
2025-08-26 15:14:29.195485:   - 未发现任何RFID标签
2025-08-26 15:14:29.195485: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:29.195485: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:29.486519: 📊 [channel_1] 主机返回当前数据: 0个条码
2025-08-26 15:14:29.691838: 🔄 开始RFID轮询检查...
2025-08-26 15:14:29.691838: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:29.691838: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:29.692836: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:29.692836:   - 设备句柄: 2528519287680
2025-08-26 15:14:29.692836:   - FetchRecords返回值: 0
2025-08-26 15:14:29.692836:   - 报告数量: 0
2025-08-26 15:14:29.693832: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:29.693832:   - 发现标签数量: 0
2025-08-26 15:14:29.693832:   - 未发现任何RFID标签
2025-08-26 15:14:29.693832: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:29.693832: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:30.191182: 🔄 开始RFID轮询检查...
2025-08-26 15:14:30.191182: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:30.191182: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:30.192179: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:30.192179:   - 设备句柄: 2528519287680
2025-08-26 15:14:30.193176:   - FetchRecords返回值: 0
2025-08-26 15:14:30.193176:   - 报告数量: 0
2025-08-26 15:14:30.193176: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:30.193176:   - 发现标签数量: 0
2025-08-26 15:14:30.194173:   - 未发现任何RFID标签
2025-08-26 15:14:30.195171: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:30.195171: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:30.553985: 接收到数据: aa 00 0a 80 00 00 1a 3a
2025-08-26 15:14:30.554980: 🔍 接收到串口数据: aa 00 0a 80 00 00 1a 3a
2025-08-26 15:14:30.555975: 🔍 数据长度: 8 字节
2025-08-26 15:14:30.555975: 🔍 预定义命令列表:
2025-08-26 15:14:30.555975:   GateCommand.enterStart: aa 00 64 80 00 00 06 d2
2025-08-26 15:14:30.555975:   GateCommand.enterEnd: aa 00 65 80 00 00 07 2e
2025-08-26 15:14:30.555975:   GateCommand.exitStart: aa 00 c8 80 00 00 27 82
2025-08-26 15:14:30.556971:   GateCommand.exitEnd: aa 00 c9 80 00 00 26 7e
2025-08-26 15:14:30.556971:   GateCommand.reachPosition: aa 00 0a 80 00 00
2025-08-26 15:14:30.556971:   GateCommand.enterOpen: aa 00 02 01 00 00 48 72
2025-08-26 15:14:30.556971:   GateCommand.exitOpen: aa 00 01 01 00 00 48 36
2025-08-26 15:14:30.556971:   GateCommand.failSignal: aa 00 02 01 00 00 48 72
2025-08-26 15:14:30.556971:   GateCommand.tailgating: aa 00 0f 80 00 00
2025-08-26 15:14:30.556971:   GateCommand.doorHasPerson: aa 00 0b 80 00 00
2025-08-26 15:14:30.557966: ✅ 解析到闸机命令: GateCommand.reachPosition
2025-08-26 15:14:30.557966: 解析到闸机命令: position_reached (到达指定位置)
2025-08-26 15:14:30.558963: 收到闸机命令: position_reached (到达指定位置)
2025-08-26 15:14:30.558963: 📍 收到到位信号，当前状态: GateState.exitStarted
2025-08-26 15:14:30.558963: 📊 流程状态：进馆=false, 出馆=true
2025-08-26 15:14:30.559961: 📊 待处理认证：进馆=false, 出馆=false
2025-08-26 15:14:30.559961: 🚪 出馆到位信号，启动认证和10秒数据收集...
2025-08-26 15:14:30.559961: 闸机状态变更: GateState.exitStarted -> GateState.exitWaitingAuth
2025-08-26 15:14:30.559961: 闸机状态更新: GateState.exitStarted -> GateState.exitWaitingAuth
2025-08-26 15:14:30.559961: 🔐 启动出馆认证系统（不关注结果）...
2025-08-26 15:14:30.559961: 闸机状态变更: GateState.exitWaitingAuth -> GateState.exitScanning
2025-08-26 15:14:30.560956: 闸机状态更新: GateState.exitWaitingAuth -> GateState.exitScanning
2025-08-26 15:14:30.560956: 多认证管理器状态变更: listening
2025-08-26 15:14:30.560956: 启动所有认证方式监听: [AuthMethod.readerCard]
2025-08-26 15:14:30.560956: 准备启动 1 个物理认证服务
2025-08-26 15:14:30.560956: 开始读卡器认证监听
2025-08-26 15:14:30.560956: 🔥 测试：跳过强制重新配置，保持现有连接
2025-08-26 15:14:30.560956: 已移除读卡器状态监听器
2025-08-26 15:14:30.560956: 已移除标签数据监听器
2025-08-26 15:14:30.560956: 所有卡片监听器已移除
2025-08-26 15:14:30.561953: 已添加读卡器状态监听器
2025-08-26 15:14:30.561953: 已添加标签数据监听器
2025-08-26 15:14:30.561953: 开始监听卡片数据 - 所有监听器已就绪
2025-08-26 15:14:30.561953: 读卡器认证监听启动成功
2025-08-26 15:14:30.561953: ✅ 出馆认证系统已启动
2025-08-26 15:14:30.561953: 🚀 启动出馆10秒数据收集...
2025-08-26 15:14:30.561953: 🔧 启动10秒计时器，当前时间: 2025-08-26 15:14:30.557966
2025-08-26 15:14:30.562950: 🔧 10秒计时器已设置，计时器对象: Instance of '_Timer'
2025-08-26 15:14:30.562950: 📡 开始从共享池收集数据...
2025-08-26 15:14:30.562950: 🔧 RFID扫描已在运行，只清空共享池准备收集新数据
2025-08-26 15:14:30.562950: 清空共享扫描池和RFID缓冲区（保持HWTagProvider标签列表）...
2025-08-26 15:14:30.562950: 🧹 开始清空共享扫描池...
2025-08-26 15:14:30.562950: 📊 清空前状态: 大小=0, 内容=[]
2025-08-26 15:14:30.562950: 🔄 重置RFID去重集合...
2025-08-26 15:14:30.562950: 🔄 开始重置已处理条码集合...
2025-08-26 15:14:30.563946: 📊 重置前状态: 大小=0, 内容=[]
2025-08-26 15:14:30.563946: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-26 15:14:30.563946: 🔄 当前场上标签将被重新识别为新标签
2025-08-26 15:14:30.563946: 📊 当前tagList状态: 0个标签
2025-08-26 15:14:30.563946: 🚀 立即触发轮询，加速标签重新发现...
2025-08-26 15:14:30.563946: 🔄 开始RFID轮询检查...
2025-08-26 15:14:30.563946: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:30.563946: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:30.563946: ✅ 已重置RFID去重集合，现有标签将被重新识别
2025-08-26 15:14:30.563946: 📡 RFID扫描状态（清空后）: isScanning=true
2025-08-26 15:14:30.564943: ✅ 共享扫描池已清空: 0 -> 0
2025-08-26 15:14:30.564943: 📡 清空通知已发送，等待RFID重新检测标签...
2025-08-26 15:14:30.564943: 清空RFID扫描缓冲区...
2025-08-26 15:14:30.564943: 🧹 已清空HWTagProvider: 0 -> 0个标签
2025-08-26 15:14:30.564943: 🔧 主机模式：清空软件缓冲区，保持硬件持续扫描
2025-08-26 15:14:30.564943: 🔄 开始重置已处理条码集合...
2025-08-26 15:14:30.564943: 📊 重置前状态: 大小=0, 内容=[]
2025-08-26 15:14:30.564943: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-26 15:14:30.564943: 🔄 当前场上标签将被重新识别为新标签
2025-08-26 15:14:30.564943: 📊 当前tagList状态: 0个标签
2025-08-26 15:14:30.565939: 🚀 立即触发轮询，加速标签重新发现...
2025-08-26 15:14:30.565939: 🔄 开始RFID轮询检查...
2025-08-26 15:14:30.565939: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:30.565939: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:30.565939: ✅ 软件缓冲区已清空，硬件扫描保持运行
2025-08-26 15:14:30.565939: [channel_1] 收到闸机事件: state_changed
2025-08-26 15:14:30.565939: 📨 收到GateCoordinator事件: state_changed
2025-08-26 15:14:30.566936: 闸机状态变更: GateState.exitWaitingAuth
2025-08-26 15:14:30.566936: 🎨 处理状态变更UI: exitWaitingAuth
2025-08-26 15:14:30.566936: 未处理的状态变更UI: exitWaitingAuth
2025-08-26 15:14:30.566936: 读者证 认证服务启动成功
2025-08-26 15:14:30.566936: 所有认证服务启动完成，成功启动 1 个服务
2025-08-26 15:14:30.566936: 当前可用的认证方式: 读者证
2025-08-26 15:14:30.566936: 🔄 开始重置已处理条码集合...
2025-08-26 15:14:30.566936: 📊 重置前状态: 大小=0, 内容=[]
2025-08-26 15:14:30.566936: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-26 15:14:30.567933: 🔄 当前场上标签将被重新识别为新标签
2025-08-26 15:14:30.567933: 📊 当前tagList状态: 0个标签
2025-08-26 15:14:30.567933: 🚀 立即触发轮询，加速标签重新发现...
2025-08-26 15:14:30.567933: 🔄 开始RFID轮询检查...
2025-08-26 15:14:30.567933: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:30.567933: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:30.567933: 共享扫描池和RFID缓冲区已清空，已处理条码集合已重置
2025-08-26 15:14:30.567933: [channel_1] 收到闸机事件: state_changed
2025-08-26 15:14:30.567933: 📨 收到GateCoordinator事件: state_changed
2025-08-26 15:14:30.567933: 闸机状态变更: GateState.exitScanning
2025-08-26 15:14:30.568931: 🎨 处理状态变更UI: exitScanning
2025-08-26 15:14:30.568931: 页面状态变更: SilencePageState.rfidScanning
2025-08-26 15:14:30.569929: 🧪 模拟模式：检测到测试卡片（调用次数: 1）
2025-08-26 15:14:30.570929: 🧪 模拟模式 - 使用测试patron: 2017621493 (索引: 0, 总调用次数: 1)
2025-08-26 15:14:30.570929: 🧪 模拟检测到卡片: 2017621493
2025-08-26 15:14:30.570929: 读卡器数据认证：
2025-08-26 15:14:30.571922:   设备类型: 10
2025-08-26 15:14:30.571922:   条码: 2017621493
2025-08-26 15:14:30.571922:   标签UID: SIM2017621493
2025-08-26 15:14:30.571922:   对应登录类型: AuthLoginType.readerCard
2025-08-26 15:14:30.571922:   根据读卡器类型10确定认证方式为: 读者证
2025-08-26 15:14:30.571922:   开始调用认证API: 2017621493
2025-08-26 15:14:30.571922: 正在认证用户: 2017621493, 方式: 读者证
2025-08-26 15:14:30.571922: 多认证管理器: 读者证获得认证请求锁
2025-08-26 15:14:30.572917: 使用门径认证接口: identifier=2017621493, method=AuthMethod.readerCard, isEnter=false
2025-08-26 15:14:30.572917: 开始门径认证: identifier=2017621493, method=AuthMethod.readerCard, isEnter=false
2025-08-26 15:14:30.572917: 发送认证请求: {deviceMac: FFFFFFFF, patronSn: 2017621493, cardSn: null, type: 2}
2025-08-26 15:14:30.572917: 设备API服务初始化完成: http://166.111.120.166:9000
2025-08-26 15:14:30.572917: 发送读者认证请求: /tunano/ldc/entrance/v1/api/door/verify
2025-08-26 15:14:30.572917: 请求数据: {"deviceMac":"FFFFFFFF","patronSn":"2017621493","cardSn":null,"type":2}
2025-08-26 15:14:30.596837: 读者认证响应: {errorCode: 0, message: 验证通过, data: null}
2025-08-26 15:14:30.596837: 认证响应: errorCode=0, message=验证通过
2025-08-26 15:14:30.596837: 认证成功，跳过用户信息获取
2025-08-26 15:14:30.597834: 门径认证结果: AuthStatus.success, 用户=认证用户
2025-08-26 15:14:30.597834:   认证结果: AuthStatus.success, 方式: 读者证
2025-08-26 15:14:30.597834: 认证结果已产生，立即停止读卡器扫描
2025-08-26 15:14:30.597834: 没有活跃的读卡器连接需要停止
2025-08-26 15:14:30.597834: 多认证管理器: 收到认证结果，立即停止所有读卡器扫描
2025-08-26 15:14:30.597834: 立即停止所有读卡器扫描
2025-08-26 15:14:30.598830: 停止读卡器认证监听
2025-08-26 15:14:30.598830: 多认证管理器: 收到认证结果: 读者证 - success
2025-08-26 15:14:30.598830: 多认证管理器状态变更: authenticating
2025-08-26 15:14:30.598830: 多认证管理器: 认证成功，保持其他认证方式继续监听
2025-08-26 15:14:30.598830: 多认证管理器状态变更: completed
2025-08-26 15:14:30.598830: 多认证管理器: 认证结果中没有读者信息，跳过认证后处理
2025-08-26 15:14:30.598830: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-26 15:14:30.598830: 已移除读卡器状态监听器
2025-08-26 15:14:30.598830: 已移除标签数据监听器
2025-08-26 15:14:30.599827: 所有卡片监听器已移除
2025-08-26 15:14:30.599827: 没有活跃的读卡器连接需要暂停
2025-08-26 15:14:30.599827: 📱 收到出馆认证结果: AuthStatus.success, 用户: 认证用户
2025-08-26 15:14:30.599827: ✅ 出馆认证API请求已发送，继续数据收集流程（不关注认证结果）
2025-08-26 15:14:30.599827: 读卡器认证监听已停止（连接保持）
2025-08-26 15:14:30.599827: 已停止 读者证 扫描
2025-08-26 15:14:30.599827: [channel_1] 收到闸机事件: exit_auth_success
2025-08-26 15:14:30.599827: 📨 收到GateCoordinator事件: exit_auth_success
2025-08-26 15:14:30.599827: 未处理的GateCoordinator事件: exit_auth_success
2025-08-26 15:14:30.692524: 🔄 开始RFID轮询检查...
2025-08-26 15:14:30.692524: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:30.692524: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:30.693517: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:30.693517:   - 设备句柄: 2528519287680
2025-08-26 15:14:30.693517:   - FetchRecords返回值: 0
2025-08-26 15:14:30.693517:   - 报告数量: 0
2025-08-26 15:14:30.694514: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:30.694514:   - 发现标签数量: 0
2025-08-26 15:14:30.694514:   - 未发现任何RFID标签
2025-08-26 15:14:30.694514: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:30.694514: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:31.191864: 🔄 开始RFID轮询检查...
2025-08-26 15:14:31.191864: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:31.191864: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:31.192861: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:31.192861:   - 设备句柄: 2528519287680
2025-08-26 15:14:31.192861:   - FetchRecords返回值: 0
2025-08-26 15:14:31.192861:   - 报告数量: 0
2025-08-26 15:14:31.192861: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:31.192861:   - 发现标签数量: 0
2025-08-26 15:14:31.193858:   - 未发现任何RFID标签
2025-08-26 15:14:31.193858: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:31.193858: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:31.692207: 🔄 开始RFID轮询检查...
2025-08-26 15:14:31.692207: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:31.692207: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:31.693206: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:31.694205:   - 设备句柄: 2528519287680
2025-08-26 15:14:31.694205:   - FetchRecords返回值: 0
2025-08-26 15:14:31.695196:   - 报告数量: 0
2025-08-26 15:14:31.695196: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:31.695196:   - 发现标签数量: 0
2025-08-26 15:14:31.695196:   - 未发现任何RFID标签
2025-08-26 15:14:31.695196: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:31.696192: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:32.192065: 🔄 开始RFID轮询检查...
2025-08-26 15:14:32.192065: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:32.192065: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:32.193062: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:32.193062:   - 设备句柄: 2528519287680
2025-08-26 15:14:32.193062:   - FetchRecords返回值: 0
2025-08-26 15:14:32.193062:   - 报告数量: 0
2025-08-26 15:14:32.194058: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:32.194058:   - 发现标签数量: 0
2025-08-26 15:14:32.194058:   - 未发现任何RFID标签
2025-08-26 15:14:32.194058: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 15:14:32.194058: 🚫 LSGate未检测到任何RFID标签
2025-08-26 15:14:32.692405: 🔄 开始RFID轮询检查...
2025-08-26 15:14:32.692405: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 15:14:32.692405: ⚠️ tagList为空，场上无标签
2025-08-26 15:14:32.694401: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:32.694401:   - 设备句柄: 2528519287680
2025-08-26 15:14:32.694401:   - FetchRecords返回值: 0
2025-08-26 15:14:32.694401:   - 报告数量: 1
2025-08-26 15:14:32.695405:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:32.695405:   - 原始数据: 01 00 19 08 1A 0F 0F 2B 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:32.695405:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:32.695405:   - 设备类型: LSGControlCenter
2025-08-26 15:14:32.696394:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:32.696394:   - 数据长度: 8
2025-08-26 15:14:32.696394:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:32.696394:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:32.697391: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:32.697391:   - 发现标签数量: 1
2025-08-26 15:14:32.697391:   - 标签详情:
2025-08-26 15:14:32.698389:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:32.698389: RFID扫描: 发现 1 个标签
2025-08-26 15:14:32.698389: 🔍 LSGate ReaderManager收到扫描结果: 1 个UID
2025-08-26 15:14:32.698389: 🎉 LSGate检测到RFID标签！
2025-08-26 15:14:32.699386: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 43], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:32.699386:   - UID: E004015304F3DD22
2025-08-26 15:14:32.699386:   - Data: E004015304F3DD22
2025-08-26 15:14:32.699386:   - EventType: 1
2025-08-26 15:14:32.699386:   - Direction: 0
2025-08-26 15:14:32.699386:   - Antenna: 1
2025-08-26 15:14:32.700379:   - TagFrequency: 0
2025-08-26 15:14:32.700379: data:E004015304F3DD22,coder:Coder15962Std
2025-08-26 15:14:32.700379: parseRet：{"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]},decoder:15962标准协议
2025-08-26 15:14:32.700379: 🎯 HWTagProvider.changeAddedItem: 收到 1 个新标签
2025-08-26 15:14:32.700379: 🏷️ 新标签[0]: uid=E004015304F3DD22, barCode=null, readerType=22
2025-08-26 15:14:32.700379: 📡 LSGate标签详情: {uid: E004015304F3DD22, barCode: null, eas: null, tagType: null, libraryCode: null, afiStr: null, afiData: null, readerType: 22, decoderType: 15962标准协议, dsfID: null, oidList: [{oid: 0, compressMode: 110, data: S, originHexStr: E004015300000000, originData: [53], isKeepInTag: true}], data: E004015304F3DD22, leftBinary: null, sortType: null, codeType: null, version: null, contentIndex: null, tagFrequency: 0, info: null, inAnt: 1}
2025-08-26 15:14:32.701376: 🎯 HWTagProvider: 当前总标签数 1, 新增标签数 1
2025-08-26 15:14:33.191763: 🔄 开始RFID轮询检查...
2025-08-26 15:14:33.192748: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=0个
2025-08-26 15:14:33.192748: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 15:14:33.192748: 🆕 轮询发现新标签(UID作为条码): E004015304F3DD22
2025-08-26 15:14:33.193743: 📋 添加到已处理列表: 0 -> 1
2025-08-26 15:14:33.193743: 🏷️ 检测到标签: E004015304F3DD22
2025-08-26 15:14:33.193743: 📊 当前扫描状态: 扫描中=true, 已扫描=1个
2025-08-26 15:14:33.193743: 📋 已扫描列表: [E004015304F3DD22]
2025-08-26 15:14:33.193743: ✅ 条码已发送到barcodeStream，将进入共享池: E004015304F3DD22
2025-08-26 15:14:33.193743: 📡 barcodeStream监听器数量: 有监听器
2025-08-26 15:14:33.193743: ✅ 轮询完成: 发现1个新标签，总计已处理1个标签
2025-08-26 15:14:33.193743: 📋 当前已处理标签列表: [E004015304F3DD22]
2025-08-26 15:14:33.194740: 🔄 尝试添加条码到共享池: E004015304F3DD22
2025-08-26 15:14:33.194740: 📊 添加前状态: 共享池大小=0, 是否为空=true
2025-08-26 15:14:33.194740: ✅ 成功添加条码到共享池: E004015304F3DD22 (总计: 1)
2025-08-26 15:14:33.194740: 📋 当前共享池内容: [E004015304F3DD22]
2025-08-26 15:14:33.194740: 📡 共享池变化通知已发送
2025-08-26 15:14:33.194740: 🔄 尝试添加条码到共享池: E004015304F3DD22
2025-08-26 15:14:33.194740: 📊 添加前状态: 共享池大小=1, 是否为空=false
2025-08-26 15:14:33.195737: 🔄 条码已存在于共享池: E004015304F3DD22 (总计: 1)
2025-08-26 15:14:33.195737: 扫描到新条码: E004015304F3DD22 (总计: 1)
2025-08-26 15:14:33.195737: 页面状态变更: SilencePageState.rfidScanning
2025-08-26 15:14:33.195737: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:33.195737:   - 设备句柄: 2528519287680
2025-08-26 15:14:33.195737:   - FetchRecords返回值: 0
2025-08-26 15:14:33.195737:   - 报告数量: 1
2025-08-26 15:14:33.195737:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:33.196733:   - 原始数据: 01 00 19 08 1A 0F 0F 2B 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:33.196733:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:33.196733:   - 设备类型: LSGControlCenter
2025-08-26 15:14:33.196733:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:33.196733:   - 数据长度: 8
2025-08-26 15:14:33.196733:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:33.196733:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:33.197730: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:33.197730:   - 发现标签数量: 1
2025-08-26 15:14:33.197730:   - 标签详情:
2025-08-26 15:14:33.197730:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:33.197730: RFID扫描: 发现 1 个标签
2025-08-26 15:14:33.197730: 🔍 LSGate ReaderManager收到扫描结果: 1 个UID
2025-08-26 15:14:33.197730: 🎉 LSGate检测到RFID标签！
2025-08-26 15:14:33.197730: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 43], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:33.198727:   - UID: E004015304F3DD22
2025-08-26 15:14:33.198727:   - Data: E004015304F3DD22
2025-08-26 15:14:33.198727:   - EventType: 1
2025-08-26 15:14:33.198727:   - Direction: 0
2025-08-26 15:14:33.198727:   - Antenna: 1
2025-08-26 15:14:33.198727:   - TagFrequency: 0
2025-08-26 15:14:33.691094: 🔄 开始RFID轮询检查...
2025-08-26 15:14:33.691094: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 15:14:33.691094: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 15:14:33.692091: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 15:14:33.692091: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 15:14:33.692091: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:33.693091:   - 设备句柄: 2528519287680
2025-08-26 15:14:33.693091:   - FetchRecords返回值: 0
2025-08-26 15:14:33.693091:   - 报告数量: 1
2025-08-26 15:14:33.693091:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:33.693091:   - 原始数据: 01 00 19 08 1A 0F 0F 2B 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:33.693091:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:33.694085:   - 设备类型: LSGControlCenter
2025-08-26 15:14:33.694085:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:33.694085:   - 数据长度: 8
2025-08-26 15:14:33.694085:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:33.694085:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:33.694085: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:33.694085:   - 发现标签数量: 1
2025-08-26 15:14:33.694085:   - 标签详情:
2025-08-26 15:14:33.695081:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:33.695081: RFID扫描: 发现 1 个标签
2025-08-26 15:14:33.695081: 🔍 LSGate ReaderManager收到扫描结果: 1 个UID
2025-08-26 15:14:33.695081: 🎉 LSGate检测到RFID标签！
2025-08-26 15:14:33.695081: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 43], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:33.695081:   - UID: E004015304F3DD22
2025-08-26 15:14:33.695081:   - Data: E004015304F3DD22
2025-08-26 15:14:33.695081:   - EventType: 1
2025-08-26 15:14:33.696078:   - Direction: 0
2025-08-26 15:14:33.696078:   - Antenna: 1
2025-08-26 15:14:33.696078:   - TagFrequency: 0
2025-08-26 15:14:34.191435: 🔄 开始RFID轮询检查...
2025-08-26 15:14:34.191435: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 15:14:34.191435: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 15:14:34.191435: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 15:14:34.192438: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 15:14:34.192438: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:34.193429:   - 设备句柄: 2528519287680
2025-08-26 15:14:34.193429:   - FetchRecords返回值: 0
2025-08-26 15:14:34.193429:   - 报告数量: 1
2025-08-26 15:14:34.194427:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:34.194427:   - 原始数据: 01 00 19 08 1A 0F 0F 2B 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:34.194427:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:34.194427:   - 设备类型: LSGControlCenter
2025-08-26 15:14:34.195423:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:34.195423:   - 数据长度: 8
2025-08-26 15:14:34.195423:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:34.195423:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:34.196421: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:34.196421:   - 发现标签数量: 1
2025-08-26 15:14:34.196421:   - 标签详情:
2025-08-26 15:14:34.196421:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:34.197417: RFID扫描: 发现 1 个标签
2025-08-26 15:14:34.197417: 🔍 LSGate ReaderManager收到扫描结果: 1 个UID
2025-08-26 15:14:34.197417: 🎉 LSGate检测到RFID标签！
2025-08-26 15:14:34.197417: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 43], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:34.197417:   - UID: E004015304F3DD22
2025-08-26 15:14:34.198412:   - Data: E004015304F3DD22
2025-08-26 15:14:34.198412:   - EventType: 1
2025-08-26 15:14:34.198412:   - Direction: 0
2025-08-26 15:14:34.198412:   - Antenna: 1
2025-08-26 15:14:34.198412:   - TagFrequency: 0
2025-08-26 15:14:34.691776: 🔄 开始RFID轮询检查...
2025-08-26 15:14:34.691776: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 15:14:34.691776: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 15:14:34.691776: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 15:14:34.691776: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 15:14:34.692773: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:34.693769:   - 设备句柄: 2528519287680
2025-08-26 15:14:34.693769:   - FetchRecords返回值: 0
2025-08-26 15:14:34.693769:   - 报告数量: 1
2025-08-26 15:14:34.693769:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:34.693769:   - 原始数据: 01 00 19 08 1A 0F 0F 2B 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:34.693769:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:34.693769:   - 设备类型: LSGControlCenter
2025-08-26 15:14:34.693769:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:34.694766:   - 数据长度: 8
2025-08-26 15:14:34.694766:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:34.694766:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:34.694766: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:34.694766:   - 发现标签数量: 1
2025-08-26 15:14:34.694766:   - 标签详情:
2025-08-26 15:14:34.694766:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:34.695763: RFID扫描: 发现 1 个标签
2025-08-26 15:14:34.695763: 🔍 LSGate ReaderManager收到扫描结果: 1 个UID
2025-08-26 15:14:34.695763: 🎉 LSGate检测到RFID标签！
2025-08-26 15:14:34.695763: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 43], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:34.695763:   - UID: E004015304F3DD22
2025-08-26 15:14:34.695763:   - Data: E004015304F3DD22
2025-08-26 15:14:34.695763:   - EventType: 1
2025-08-26 15:14:34.695763:   - Direction: 0
2025-08-26 15:14:34.696759:   - Antenna: 1
2025-08-26 15:14:34.696759:   - TagFrequency: 0
2025-08-26 15:14:35.192682: 🔄 开始RFID轮询检查...
2025-08-26 15:14:35.192682: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 15:14:35.192682: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 15:14:35.192682: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 15:14:35.192682: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 15:14:35.193679: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:35.194675:   - 设备句柄: 2528519287680
2025-08-26 15:14:35.194675:   - FetchRecords返回值: 0
2025-08-26 15:14:35.194675:   - 报告数量: 1
2025-08-26 15:14:35.194675:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:35.194675:   - 原始数据: 01 00 19 08 1A 0F 0F 2B 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:35.194675:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:35.195674:   - 设备类型: LSGControlCenter
2025-08-26 15:14:35.195674:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:35.195674:   - 数据长度: 8
2025-08-26 15:14:35.195674:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:35.195674:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:35.195674: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:35.195674:   - 发现标签数量: 1
2025-08-26 15:14:35.195674:   - 标签详情:
2025-08-26 15:14:35.196668:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:35.196668: RFID扫描: 发现 1 个标签
2025-08-26 15:14:35.196668: 🔍 LSGate ReaderManager收到扫描结果: 1 个UID
2025-08-26 15:14:35.196668: 🎉 LSGate检测到RFID标签！
2025-08-26 15:14:35.196668: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 43], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:35.196668:   - UID: E004015304F3DD22
2025-08-26 15:14:35.196668:   - Data: E004015304F3DD22
2025-08-26 15:14:35.197665:   - EventType: 1
2025-08-26 15:14:35.197665:   - Direction: 0
2025-08-26 15:14:35.197665:   - Antenna: 1
2025-08-26 15:14:35.197665:   - TagFrequency: 0
2025-08-26 15:14:35.600330: 多认证管理器: 认证请求锁已释放（之前为读者证）
2025-08-26 15:14:35.692026: 🔄 开始RFID轮询检查...
2025-08-26 15:14:35.692026: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 15:14:35.692026: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 15:14:35.693024: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 15:14:35.693024: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 15:14:35.693024: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:35.694020:   - 设备句柄: 2528519287680
2025-08-26 15:14:35.694020:   - FetchRecords返回值: 0
2025-08-26 15:14:35.695017:   - 报告数量: 1
2025-08-26 15:14:35.695017:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:35.695017:   - 原始数据: 01 00 19 08 1A 0F 0F 2B 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:35.695017:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:35.695017:   - 设备类型: LSGControlCenter
2025-08-26 15:14:35.696013:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:35.696013:   - 数据长度: 8
2025-08-26 15:14:35.696013:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:35.696013:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:35.696013: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:35.696013:   - 发现标签数量: 1
2025-08-26 15:14:35.697009:   - 标签详情:
2025-08-26 15:14:35.697009:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:35.697009: RFID扫描: 发现 1 个标签
2025-08-26 15:14:35.697009: 🔍 LSGate ReaderManager收到扫描结果: 1 个UID
2025-08-26 15:14:35.697009: 🎉 LSGate检测到RFID标签！
2025-08-26 15:14:35.697009: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 43], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:35.697009:   - UID: E004015304F3DD22
2025-08-26 15:14:35.697009:   - Data: E004015304F3DD22
2025-08-26 15:14:35.698006:   - EventType: 1
2025-08-26 15:14:35.698006:   - Direction: 0
2025-08-26 15:14:35.698006:   - Antenna: 1
2025-08-26 15:14:35.698006:   - TagFrequency: 0
2025-08-26 15:14:36.191370: 🔄 开始RFID轮询检查...
2025-08-26 15:14:36.191370: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 15:14:36.191370: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 15:14:36.191370: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 15:14:36.191370: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 15:14:36.192367: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:36.193363:   - 设备句柄: 2528519287680
2025-08-26 15:14:36.193363:   - FetchRecords返回值: 0
2025-08-26 15:14:36.193363:   - 报告数量: 1
2025-08-26 15:14:36.193363:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:36.193363:   - 原始数据: 01 00 19 08 1A 0F 0F 2B 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:36.193363:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:36.194360:   - 设备类型: LSGControlCenter
2025-08-26 15:14:36.194360:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:36.194360:   - 数据长度: 8
2025-08-26 15:14:36.194360:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:36.194360:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:36.194360: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:36.194360:   - 发现标签数量: 1
2025-08-26 15:14:36.194360:   - 标签详情:
2025-08-26 15:14:36.195357:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:36.195357: RFID扫描: 发现 1 个标签
2025-08-26 15:14:36.195357: 🔍 LSGate ReaderManager收到扫描结果: 1 个UID
2025-08-26 15:14:36.195357: 🎉 LSGate检测到RFID标签！
2025-08-26 15:14:36.195357: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 43], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:36.195357:   - UID: E004015304F3DD22
2025-08-26 15:14:36.195357:   - Data: E004015304F3DD22
2025-08-26 15:14:36.196353:   - EventType: 1
2025-08-26 15:14:36.196353:   - Direction: 0
2025-08-26 15:14:36.196353:   - Antenna: 1
2025-08-26 15:14:36.196353:   - TagFrequency: 0
2025-08-26 15:14:36.692707: 🔄 开始RFID轮询检查...
2025-08-26 15:14:36.692707: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 15:14:36.692707: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 15:14:36.692707: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 15:14:36.692707: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 15:14:36.694701: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:36.694701:   - 设备句柄: 2528519287680
2025-08-26 15:14:36.694701:   - FetchRecords返回值: 0
2025-08-26 15:14:36.694701:   - 报告数量: 1
2025-08-26 15:14:36.695711:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:36.695711:   - 原始数据: 01 00 19 08 1A 0F 0F 2B 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:36.695711:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:36.695711:   - 设备类型: LSGControlCenter
2025-08-26 15:14:36.695711:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:36.696695:   - 数据长度: 8
2025-08-26 15:14:36.696695:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:36.696695:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:36.696695: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:36.697696:   - 发现标签数量: 1
2025-08-26 15:14:36.697696:   - 标签详情:
2025-08-26 15:14:36.698690:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:36.698690: RFID扫描: 发现 1 个标签
2025-08-26 15:14:36.698690: 🔍 LSGate ReaderManager收到扫描结果: 1 个UID
2025-08-26 15:14:36.698690: 🎉 LSGate检测到RFID标签！
2025-08-26 15:14:36.699685: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 43], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:36.699685:   - UID: E004015304F3DD22
2025-08-26 15:14:36.699685:   - Data: E004015304F3DD22
2025-08-26 15:14:36.699685:   - EventType: 1
2025-08-26 15:14:36.699685:   - Direction: 0
2025-08-26 15:14:36.699685:   - Antenna: 1
2025-08-26 15:14:36.699685:   - TagFrequency: 0
2025-08-26 15:14:37.191058: 🔄 开始RFID轮询检查...
2025-08-26 15:14:37.192057: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 15:14:37.192057: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 15:14:37.192057: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 15:14:37.192057: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 15:14:37.193049: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:37.193049:   - 设备句柄: 2528519287680
2025-08-26 15:14:37.193049:   - FetchRecords返回值: 0
2025-08-26 15:14:37.194047:   - 报告数量: 1
2025-08-26 15:14:37.194047:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:37.194047:   - 原始数据: 01 00 19 08 1A 0F 0F 2B 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:37.194047:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:37.194047:   - 设备类型: LSGControlCenter
2025-08-26 15:14:37.194047:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:37.194047:   - 数据长度: 8
2025-08-26 15:14:37.194047:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:37.195042:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:37.195042: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:37.195042:   - 发现标签数量: 1
2025-08-26 15:14:37.195042:   - 标签详情:
2025-08-26 15:14:37.195042:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:37.195042: RFID扫描: 发现 1 个标签
2025-08-26 15:14:37.195042: 🔍 LSGate ReaderManager收到扫描结果: 1 个UID
2025-08-26 15:14:37.196039: 🎉 LSGate检测到RFID标签！
2025-08-26 15:14:37.196039: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 43], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:37.196039:   - UID: E004015304F3DD22
2025-08-26 15:14:37.196039:   - Data: E004015304F3DD22
2025-08-26 15:14:37.196039:   - EventType: 1
2025-08-26 15:14:37.196039:   - Direction: 0
2025-08-26 15:14:37.196039:   - Antenna: 1
2025-08-26 15:14:37.196039:   - TagFrequency: 0
2025-08-26 15:14:37.692392: 🔄 开始RFID轮询检查...
2025-08-26 15:14:37.692392: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 15:14:37.692392: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 15:14:37.693390: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 15:14:37.693390: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 15:14:37.693390: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:37.694386:   - 设备句柄: 2528519287680
2025-08-26 15:14:37.694386:   - FetchRecords返回值: 0
2025-08-26 15:14:37.694386:   - 报告数量: 1
2025-08-26 15:14:37.694386:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:37.694386:   - 原始数据: 01 00 19 08 1A 0F 0F 2B 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:37.694386:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:37.695383:   - 设备类型: LSGControlCenter
2025-08-26 15:14:37.695383:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:37.695383:   - 数据长度: 8
2025-08-26 15:14:37.695383:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:37.695383:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:37.695383: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:37.695383:   - 发现标签数量: 1
2025-08-26 15:14:37.695383:   - 标签详情:
2025-08-26 15:14:37.696379:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:37.696379: RFID扫描: 发现 1 个标签
2025-08-26 15:14:37.696379: 🔍 LSGate ReaderManager收到扫描结果: 1 个UID
2025-08-26 15:14:37.696379: 🎉 LSGate检测到RFID标签！
2025-08-26 15:14:37.696379: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 43], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:37.696379:   - UID: E004015304F3DD22
2025-08-26 15:14:37.696379:   - Data: E004015304F3DD22
2025-08-26 15:14:37.696379:   - EventType: 1
2025-08-26 15:14:37.697376:   - Direction: 0
2025-08-26 15:14:37.697376:   - Antenna: 1
2025-08-26 15:14:37.697376:   - TagFrequency: 0
2025-08-26 15:14:38.192257: 🔄 开始RFID轮询检查...
2025-08-26 15:14:38.192257: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 15:14:38.193255: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 15:14:38.193255: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 15:14:38.193255: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 15:14:38.193255: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:38.194254:   - 设备句柄: 2528519287680
2025-08-26 15:14:38.194254:   - FetchRecords返回值: 0
2025-08-26 15:14:38.194254:   - 报告数量: 1
2025-08-26 15:14:38.194254:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:38.195249:   - 原始数据: 01 00 19 08 1A 0F 0F 2B 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:38.195249:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:38.195249:   - 设备类型: LSGControlCenter
2025-08-26 15:14:38.195249:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:38.195249:   - 数据长度: 8
2025-08-26 15:14:38.196245:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:38.196245:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:38.196245: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:38.197243:   - 发现标签数量: 1
2025-08-26 15:14:38.197243:   - 标签详情:
2025-08-26 15:14:38.197243:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:38.197243: RFID扫描: 发现 1 个标签
2025-08-26 15:14:38.197243: 🔍 LSGate ReaderManager收到扫描结果: 1 个UID
2025-08-26 15:14:38.198238: 🎉 LSGate检测到RFID标签！
2025-08-26 15:14:38.198238: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 43], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:38.198238:   - UID: E004015304F3DD22
2025-08-26 15:14:38.198238:   - Data: E004015304F3DD22
2025-08-26 15:14:38.198238:   - EventType: 1
2025-08-26 15:14:38.198238:   - Direction: 0
2025-08-26 15:14:38.199234:   - Antenna: 1
2025-08-26 15:14:38.199234:   - TagFrequency: 0
2025-08-26 15:14:38.249070: 发送心跳
2025-08-26 15:14:38.249070: Req msgType：Sip2MsgType.scStatus ,length:20， ret:  9900522.00AY3AZFC9E
2025-08-26 15:14:38.310864: Rsp : 98YYYNNN00500320250826    1514312.00AOhlsp|AM海恒图书馆|BXYYYYYYYYYYYYYYYY|AN3a40852d-49fd-4df2-a1f9-6e2641a6e91f|AF|AG|AY3AZD521
2025-08-26 15:14:38.599905: 多认证管理器: 认证结果显示完成，恢复到监听状态
2025-08-26 15:14:38.599905: 多认证管理器状态变更: listening
2025-08-26 15:14:38.599905: ⚠️ 人脸识别服务未初始化或不可用
2025-08-26 15:14:38.692598: 🔄 开始RFID轮询检查...
2025-08-26 15:14:38.692598: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 15:14:38.692598: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 15:14:38.692598: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 15:14:38.692598: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 15:14:38.693595: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:38.693595:   - 设备句柄: 2528519287680
2025-08-26 15:14:38.694592:   - FetchRecords返回值: 0
2025-08-26 15:14:38.694592:   - 报告数量: 1
2025-08-26 15:14:38.694592:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:38.694592:   - 原始数据: 01 00 19 08 1A 0F 0F 2B 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:38.694592:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:38.694592:   - 设备类型: LSGControlCenter
2025-08-26 15:14:38.694592:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:38.695588:   - 数据长度: 8
2025-08-26 15:14:38.695588:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:38.695588:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:38.695588: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:38.695588:   - 发现标签数量: 1
2025-08-26 15:14:38.695588:   - 标签详情:
2025-08-26 15:14:38.695588:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:38.695588: RFID扫描: 发现 1 个标签
2025-08-26 15:14:38.696585: 🔍 LSGate ReaderManager收到扫描结果: 1 个UID
2025-08-26 15:14:38.696585: 🎉 LSGate检测到RFID标签！
2025-08-26 15:14:38.696585: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 43], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:38.696585:   - UID: E004015304F3DD22
2025-08-26 15:14:38.696585:   - Data: E004015304F3DD22
2025-08-26 15:14:38.696585:   - EventType: 1
2025-08-26 15:14:38.696585:   - Direction: 0
2025-08-26 15:14:38.697582:   - Antenna: 1
2025-08-26 15:14:38.697582:   - TagFrequency: 0
2025-08-26 15:14:39.192939: 🔄 开始RFID轮询检查...
2025-08-26 15:14:39.192939: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 15:14:39.192939: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 15:14:39.192939: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 15:14:39.193936: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 15:14:39.193936: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:39.194932:   - 设备句柄: 2528519287680
2025-08-26 15:14:39.194932:   - FetchRecords返回值: 0
2025-08-26 15:14:39.194932:   - 报告数量: 1
2025-08-26 15:14:39.194932:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:39.194932:   - 原始数据: 01 00 19 08 1A 0F 0F 2B 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:39.194932:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:39.194932:   - 设备类型: LSGControlCenter
2025-08-26 15:14:39.195929:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:39.195929:   - 数据长度: 8
2025-08-26 15:14:39.195929:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:39.195929:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:39.195929: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:39.195929:   - 发现标签数量: 1
2025-08-26 15:14:39.195929:   - 标签详情:
2025-08-26 15:14:39.196926:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:39.196926: RFID扫描: 发现 1 个标签
2025-08-26 15:14:39.196926: 🔍 LSGate ReaderManager收到扫描结果: 1 个UID
2025-08-26 15:14:39.196926: 🎉 LSGate检测到RFID标签！
2025-08-26 15:14:39.196926: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 43], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:39.196926:   - UID: E004015304F3DD22
2025-08-26 15:14:39.196926:   - Data: E004015304F3DD22
2025-08-26 15:14:39.196926:   - EventType: 1
2025-08-26 15:14:39.197923:   - Direction: 0
2025-08-26 15:14:39.197923:   - Antenna: 1
2025-08-26 15:14:39.197923:   - TagFrequency: 0
2025-08-26 15:14:39.691306: 🔄 开始RFID轮询检查...
2025-08-26 15:14:39.691306: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 15:14:39.692284: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 15:14:39.692284: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 15:14:39.692284: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 15:14:39.693286: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:39.693286:   - 设备句柄: 2528519287680
2025-08-26 15:14:39.694280:   - FetchRecords返回值: 0
2025-08-26 15:14:39.694280:   - 报告数量: 1
2025-08-26 15:14:39.694280:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:39.694280:   - 原始数据: 01 00 19 08 1A 0F 0F 2B 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:39.695277:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:39.695277:   - 设备类型: LSGControlCenter
2025-08-26 15:14:39.695277:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:39.695277:   - 数据长度: 8
2025-08-26 15:14:39.695277:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:39.696271:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:39.696271: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:39.696271:   - 发现标签数量: 1
2025-08-26 15:14:39.696271:   - 标签详情:
2025-08-26 15:14:39.696271:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:39.696271: RFID扫描: 发现 1 个标签
2025-08-26 15:14:39.696271: 🔍 LSGate ReaderManager收到扫描结果: 1 个UID
2025-08-26 15:14:39.697267: 🎉 LSGate检测到RFID标签！
2025-08-26 15:14:39.697267: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 43], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:39.697267:   - UID: E004015304F3DD22
2025-08-26 15:14:39.697267:   - Data: E004015304F3DD22
2025-08-26 15:14:39.697267:   - EventType: 1
2025-08-26 15:14:39.697267:   - Direction: 0
2025-08-26 15:14:39.697267:   - Antenna: 1
2025-08-26 15:14:39.698264:   - TagFrequency: 0
2025-08-26 15:14:40.191671: 🔄 开始RFID轮询检查...
2025-08-26 15:14:40.191671: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 15:14:40.191671: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 15:14:40.191671: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 15:14:40.191671: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 15:14:40.192668: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:40.193665:   - 设备句柄: 2528519287680
2025-08-26 15:14:40.193665:   - FetchRecords返回值: 0
2025-08-26 15:14:40.193665:   - 报告数量: 1
2025-08-26 15:14:40.193665:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:40.193665:   - 原始数据: 01 00 19 08 1A 0F 0F 2B 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:40.193665:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:40.193665:   - 设备类型: LSGControlCenter
2025-08-26 15:14:40.193665:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:40.194661:   - 数据长度: 8
2025-08-26 15:14:40.194661:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:40.194661:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:40.194661: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:40.194661:   - 发现标签数量: 1
2025-08-26 15:14:40.194661:   - 标签详情:
2025-08-26 15:14:40.194661:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:40.194661: RFID扫描: 发现 1 个标签
2025-08-26 15:14:40.195658: 🔍 LSGate ReaderManager收到扫描结果: 1 个UID
2025-08-26 15:14:40.195658: 🎉 LSGate检测到RFID标签！
2025-08-26 15:14:40.195658: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 43], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:40.195658:   - UID: E004015304F3DD22
2025-08-26 15:14:40.195658:   - Data: E004015304F3DD22
2025-08-26 15:14:40.195658:   - EventType: 1
2025-08-26 15:14:40.195658:   - Direction: 0
2025-08-26 15:14:40.196657:   - Antenna: 1
2025-08-26 15:14:40.196657:   - TagFrequency: 0
2025-08-26 15:14:40.560448: ⏰ 10秒数据收集完成，开始书籍检查... 时间: 2025-08-26 15:14:40.560448
2025-08-26 15:14:40.561446: 🛑 停止出馆数据收集，收集到UID数量: 0
2025-08-26 15:14:40.561446: 🔧 停止时间: 2025-08-26 15:14:40.560448
2025-08-26 15:14:40.561446: 📡 停止从共享池收集数据...
2025-08-26 15:14:40.562446: 🔧 保持RFID扫描运行，只停止从共享池收集数据
2025-08-26 15:14:40.562446: 📡 开始从RFID服务收集UID数据...
2025-08-26 15:14:40.562446: 📊 从HWTagProvider获取UID，标签数量: 1
2025-08-26 15:14:40.562446: 📡 收集到UID: E004015304F3DD22 (条码: null)
2025-08-26 15:14:40.563443: 📡 从RFID服务收集到UID数量: 1
2025-08-26 15:14:40.563443: 📋 UID列表: E004015304F3DD22
2025-08-26 15:14:40.563443: 📊 数据收集完成，UID列表: [E004015304F3DD22]
2025-08-26 15:14:40.563443: 🔍 开始三步书籍检查，UID数量: 1
2025-08-26 15:14:40.563443: 闸机状态变更: GateState.exitScanning -> GateState.exitChecking
2025-08-26 15:14:40.563443: 闸机状态更新: GateState.exitScanning -> GateState.exitChecking
2025-08-26 15:14:40.564435: 🚀 开始完整的三步书籍检查流程，UID数量: 1
2025-08-26 15:14:40.564435: 🔍 第一步：检查白名单，UID数量: 1
2025-08-26 15:14:40.564435: 🔍 输入UID列表: [E004015304F3DD22]
2025-08-26 15:14:40.564435: [channel_1] 收到闸机事件: state_changed
2025-08-26 15:14:40.565435: 📨 收到GateCoordinator事件: state_changed
2025-08-26 15:14:40.565435: 闸机状态变更: GateState.exitChecking
2025-08-26 15:14:40.565435: 🎨 处理状态变更UI: exitChecking
2025-08-26 15:14:40.565435: 未处理的状态变更UI: exitChecking
2025-08-26 15:14:40.566429: 
2025-08-26 15:14:40.566429: 📤 ========== 白名单检查接口 ==========
2025-08-26 15:14:40.566429: 📤 接口名称: 查询图书是否在白名单
2025-08-26 15:14:40.566429: 📤 请求URL: http://166.111.120.166:9000/tunano/ldc/white/tag/whitelist/contain
2025-08-26 15:14:40.566429: 📤 请求方法: POST
2025-08-26 15:14:40.566429: 📤 请求头: Content-Type: application/json
2025-08-26 15:14:40.566429: 📤 请求参数: {"Taglist":[{"Tid":"E004015304F3DD22"}]}
2025-08-26 15:14:40.567425: 📤 请求参数解析:
2025-08-26 15:14:40.567425: 📤   - Taglist: 标签列表，包含1个UID
2025-08-26 15:14:40.567425: 📤     [0] Tid: E004015304F3DD22
2025-08-26 15:14:40.567425: 📤 =====================================
2025-08-26 15:14:40.567425: 
2025-08-26 15:14:40.692012: 🔄 开始RFID轮询检查...
2025-08-26 15:14:40.692012: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 15:14:40.692012: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 15:14:40.693009: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 15:14:40.693009: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 15:14:40.694006: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:40.694006:   - 设备句柄: 2528519287680
2025-08-26 15:14:40.694006:   - FetchRecords返回值: 0
2025-08-26 15:14:40.694006:   - 报告数量: 2
2025-08-26 15:14:40.695002:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:40.695002:   - 原始数据: 01 00 19 08 1A 0F 0F 2B 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:40.695002:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:40.695002:   - 设备类型: LSGControlCenter
2025-08-26 15:14:40.696001:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:40.696001:   - 数据长度: 8
2025-08-26 15:14:40.696001:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:40.696001:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:40.696001:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:40.696996:   - 原始数据: 01 00 19 08 1A 0F 0F 33 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:40.696996:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:40.696996:   - 设备类型: LSGControlCenter
2025-08-26 15:14:40.696996:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:40.696996:   - 数据长度: 8
2025-08-26 15:14:40.697998:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:40.697998:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:40.697998: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:40.698992:   - 发现标签数量: 2
2025-08-26 15:14:40.699991:   - 标签详情:
2025-08-26 15:14:40.699991:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:40.699991:     [1] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:40.699991: RFID扫描: 发现 2 个标签
2025-08-26 15:14:40.700984: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 15:14:40.700984: 🎉 LSGate检测到RFID标签！
2025-08-26 15:14:40.700984: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 43], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:40.700984:   - UID: E004015304F3DD22
2025-08-26 15:14:40.701980:   - Data: E004015304F3DD22
2025-08-26 15:14:40.701980:   - EventType: 1
2025-08-26 15:14:40.701980:   - Direction: 0
2025-08-26 15:14:40.701980:   - Antenna: 1
2025-08-26 15:14:40.701980:   - TagFrequency: 0
2025-08-26 15:14:40.702976: 🏷️ LSGate UID[1]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 51], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:40.702976:   - UID: E004015304F3DD22
2025-08-26 15:14:40.702976:   - Data: E004015304F3DD22
2025-08-26 15:14:40.702976:   - EventType: 1
2025-08-26 15:14:40.702976:   - Direction: 0
2025-08-26 15:14:40.702976:   - Antenna: 1
2025-08-26 15:14:40.702976:   - TagFrequency: 0
2025-08-26 15:14:41.192011: 🔄 开始RFID轮询检查...
2025-08-26 15:14:41.193: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 15:14:41.193: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 15:14:41.193: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 15:14:41.193: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 15:14:41.193997: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:41.193997:   - 设备句柄: 2528519287680
2025-08-26 15:14:41.193997:   - FetchRecords返回值: 0
2025-08-26 15:14:41.194994:   - 报告数量: 2
2025-08-26 15:14:41.194994:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:41.194994:   - 原始数据: 01 00 19 08 1A 0F 0F 2B 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:41.194994:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:41.194994:   - 设备类型: LSGControlCenter
2025-08-26 15:14:41.194994:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:41.194994:   - 数据长度: 8
2025-08-26 15:14:41.195990:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:41.195990:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:41.195990:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:41.195990:   - 原始数据: 01 00 19 08 1A 0F 0F 33 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:41.195990:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:41.195990:   - 设备类型: LSGControlCenter
2025-08-26 15:14:41.195990:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:41.196987:   - 数据长度: 8
2025-08-26 15:14:41.196987:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:41.196987:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:41.196987: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:41.196987:   - 发现标签数量: 2
2025-08-26 15:14:41.196987:   - 标签详情:
2025-08-26 15:14:41.196987:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:41.197986:     [1] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:41.197986: RFID扫描: 发现 2 个标签
2025-08-26 15:14:41.197986: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 15:14:41.197986: 🎉 LSGate检测到RFID标签！
2025-08-26 15:14:41.197986: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 43], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:41.197986:   - UID: E004015304F3DD22
2025-08-26 15:14:41.197986:   - Data: E004015304F3DD22
2025-08-26 15:14:41.197986:   - EventType: 1
2025-08-26 15:14:41.198979:   - Direction: 0
2025-08-26 15:14:41.198979:   - Antenna: 1
2025-08-26 15:14:41.198979:   - TagFrequency: 0
2025-08-26 15:14:41.198979: 🏷️ LSGate UID[1]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 51], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:41.198979:   - UID: E004015304F3DD22
2025-08-26 15:14:41.198979:   - Data: E004015304F3DD22
2025-08-26 15:14:41.198979:   - EventType: 1
2025-08-26 15:14:41.198979:   - Direction: 0
2025-08-26 15:14:41.199976:   - Antenna: 1
2025-08-26 15:14:41.199976:   - TagFrequency: 0
2025-08-26 15:14:41.571744: 
2025-08-26 15:14:41.571744: 📥 ========== 白名单检查响应 ==========
2025-08-26 15:14:41.571744: 📥 响应状态码: 502
2025-08-26 15:14:41.572740: 📥 响应原始数据: <html>
2025-08-26 15:14:41.572740: <head><title>502 Bad Gateway</title></head>
2025-08-26 15:14:41.572740: <body>
2025-08-26 15:14:41.572740: <center><h1>502 Bad Gateway</h1></center>
2025-08-26 15:14:41.572740: <hr><center>nginx/1.24.0</center>
2025-08-26 15:14:41.572740: </body>
2025-08-26 15:14:41.572740: </html>
2025-08-26 15:14:41.572740: 
2025-08-26 15:14:41.572740: 
2025-08-26 15:14:41.573736: ❌ HTTP请求失败: 502
2025-08-26 15:14:41.573736: ❌ 错误响应: <html>
2025-08-26 15:14:41.573736: <head><title>502 Bad Gateway</title></head>
2025-08-26 15:14:41.573736: <body>
2025-08-26 15:14:41.573736: <center><h1>502 Bad Gateway</h1></center>
2025-08-26 15:14:41.573736: <hr><center>nginx/1.24.0</center>
2025-08-26 15:14:41.573736: </body>
2025-08-26 15:14:41.573736: </html>
2025-08-26 15:14:41.573736: 
2025-08-26 15:14:41.573736: 📥 =====================================
2025-08-26 15:14:41.573736: 
2025-08-26 15:14:41.574733: ❌ 白名单检查异常: Exception: 白名单检查请求失败: 502
2025-08-26 15:14:41.574733: 📥 =====================================
2025-08-26 15:14:41.574733: 
2025-08-26 15:14:41.574733: ❌ 三步检查流程失败: Exception: 白名单检查请求失败: 502
2025-08-26 15:14:41.574733: ✅ 三步检查完成: 书籍检查服务异常，默认允许通过
2025-08-26 15:14:41.574733: 📊 检查结果: 允许通过=true, 不在白名单书籍数量=0
2025-08-26 15:14:41.574733: ✅ 允许出馆: 书籍检查服务异常，默认允许通过
2025-08-26 15:14:41.574733: ✅ 命令顺序正确：出馆流程中且最后命令是到位信号
2025-08-26 15:14:41.574733: 📤 正在发送成功信号到闸机：AA 00 01 01 00 00 48 36
2025-08-26 15:14:41.574733: 📤 准备发送闸机命令: success_signal
2025-08-26 15:14:41.575730: 📤 成功信号已发送（异步）
2025-08-26 15:14:41.575730: 闸机状态变更: GateState.exitChecking -> GateState.exitOver
2025-08-26 15:14:41.575730: 闸机状态更新: GateState.exitChecking -> GateState.exitOver
2025-08-26 15:14:41.575730: 发送原始命令数据: aa 00 01 01 00 00 48 36
2025-08-26 15:14:41.575730: 发送原始数据: aa 00 01 01 00 00 48 36
2025-08-26 15:14:41.575730: 发送数据成功: aa 00 01 01 00 00 48 36
2025-08-26 15:14:41.575730: [channel_1] 收到闸机事件: state_changed
2025-08-26 15:14:41.576727: 📨 收到GateCoordinator事件: state_changed
2025-08-26 15:14:41.576727: 闸机状态变更: GateState.exitOver
2025-08-26 15:14:41.576727: 🎨 处理状态变更UI: exitOver
2025-08-26 15:14:41.576727: 未处理的状态变更UI: exitOver
2025-08-26 15:14:41.576727: 发送数据成功: aa 00 01 01 00 00 48 36
2025-08-26 15:14:41.576727: 闸机命令发送成功: success_signal
2025-08-26 15:14:41.576727: ✅ 闸机命令 success_signal 发送成功
2025-08-26 15:14:41.576727: [channel_1] 收到闸机事件: exit_allowed
2025-08-26 15:14:41.577723: 📨 收到GateCoordinator事件: exit_allowed
2025-08-26 15:14:41.577723: 页面状态变更: SilencePageState.exitAllowed
2025-08-26 15:14:41.614601: 接收到数据: aa 00 01 81 00 00 49 de
2025-08-26 15:14:41.614601: 🔍 接收到串口数据: aa 00 01 81 00 00 49 de
2025-08-26 15:14:41.614601: 🔍 数据长度: 8 字节
2025-08-26 15:14:41.614601: 🔍 预定义命令列表:
2025-08-26 15:14:41.615599:   GateCommand.enterStart: aa 00 64 80 00 00 06 d2
2025-08-26 15:14:41.615599:   GateCommand.enterEnd: aa 00 65 80 00 00 07 2e
2025-08-26 15:14:41.615599:   GateCommand.exitStart: aa 00 c8 80 00 00 27 82
2025-08-26 15:14:41.615599:   GateCommand.exitEnd: aa 00 c9 80 00 00 26 7e
2025-08-26 15:14:41.615599:   GateCommand.reachPosition: aa 00 0a 80 00 00
2025-08-26 15:14:41.616595:   GateCommand.enterOpen: aa 00 02 01 00 00 48 72
2025-08-26 15:14:41.616595:   GateCommand.exitOpen: aa 00 01 01 00 00 48 36
2025-08-26 15:14:41.616595:   GateCommand.failSignal: aa 00 02 01 00 00 48 72
2025-08-26 15:14:41.616595:   GateCommand.tailgating: aa 00 0f 80 00 00
2025-08-26 15:14:41.617594:   GateCommand.doorHasPerson: aa 00 0b 80 00 00
2025-08-26 15:14:41.617594: ❌ 未识别的闸机命令 - 数据不匹配任何预定义命令
2025-08-26 15:14:41.617594: ❌ 接收数据: aa 00 01 81 00 00 49 de
2025-08-26 15:14:41.692343: 🔄 开始RFID轮询检查...
2025-08-26 15:14:41.692343: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 15:14:41.692343: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 15:14:41.692343: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 15:14:41.693340: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 15:14:41.694336: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:41.694336:   - 设备句柄: 2528519287680
2025-08-26 15:14:41.694336:   - FetchRecords返回值: 0
2025-08-26 15:14:41.694336:   - 报告数量: 2
2025-08-26 15:14:41.695333:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:41.695333:   - 原始数据: 01 00 19 08 1A 0F 0F 2B 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:41.695333:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:41.695333:   - 设备类型: LSGControlCenter
2025-08-26 15:14:41.695333:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:41.695333:   - 数据长度: 8
2025-08-26 15:14:41.695333:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:41.696330:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:41.696330:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:41.696330:   - 原始数据: 01 00 19 08 1A 0F 0F 33 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:41.696330:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:41.696330:   - 设备类型: LSGControlCenter
2025-08-26 15:14:41.696330:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:41.696330:   - 数据长度: 8
2025-08-26 15:14:41.697327:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:41.697327:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:41.697327: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:41.697327:   - 发现标签数量: 2
2025-08-26 15:14:41.697327:   - 标签详情:
2025-08-26 15:14:41.697327:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:41.697327:     [1] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:41.698323: RFID扫描: 发现 2 个标签
2025-08-26 15:14:41.698323: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 15:14:41.698323: 🎉 LSGate检测到RFID标签！
2025-08-26 15:14:41.698323: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 43], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:41.698323:   - UID: E004015304F3DD22
2025-08-26 15:14:41.698323:   - Data: E004015304F3DD22
2025-08-26 15:14:41.698323:   - EventType: 1
2025-08-26 15:14:41.699320:   - Direction: 0
2025-08-26 15:14:41.699320:   - Antenna: 1
2025-08-26 15:14:41.699320:   - TagFrequency: 0
2025-08-26 15:14:41.699320: 🏷️ LSGate UID[1]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 51], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:41.699320:   - UID: E004015304F3DD22
2025-08-26 15:14:41.699320:   - Data: E004015304F3DD22
2025-08-26 15:14:41.699320:   - EventType: 1
2025-08-26 15:14:41.699320:   - Direction: 0
2025-08-26 15:14:41.700317:   - Antenna: 1
2025-08-26 15:14:41.700317:   - TagFrequency: 0
2025-08-26 15:14:42.192705: 🔄 开始RFID轮询检查...
2025-08-26 15:14:42.192705: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 15:14:42.193681: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 15:14:42.193681: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 15:14:42.193681: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 15:14:42.194683: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:42.194683:   - 设备句柄: 2528519287680
2025-08-26 15:14:42.195676:   - FetchRecords返回值: 0
2025-08-26 15:14:42.195676:   - 报告数量: 2
2025-08-26 15:14:42.195676:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:42.195676:   - 原始数据: 01 00 19 08 1A 0F 0F 2B 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:42.196674:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:42.196674:   - 设备类型: LSGControlCenter
2025-08-26 15:14:42.196674:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:42.197671:   - 数据长度: 8
2025-08-26 15:14:42.197671:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:42.197671:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:42.197671:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:42.198665:   - 原始数据: 01 00 19 08 1A 0F 0F 33 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:42.198665:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:42.198665:   - 设备类型: LSGControlCenter
2025-08-26 15:14:42.198665:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:42.199662:   - 数据长度: 8
2025-08-26 15:14:42.199662:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:42.199662:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:42.199662: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:42.199662:   - 发现标签数量: 2
2025-08-26 15:14:42.200657:   - 标签详情:
2025-08-26 15:14:42.200657:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:42.200657:     [1] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:42.200657: RFID扫描: 发现 2 个标签
2025-08-26 15:14:42.200657: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 15:14:42.200657: 🎉 LSGate检测到RFID标签！
2025-08-26 15:14:42.200657: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 43], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:42.201655:   - UID: E004015304F3DD22
2025-08-26 15:14:42.201655:   - Data: E004015304F3DD22
2025-08-26 15:14:42.201655:   - EventType: 1
2025-08-26 15:14:42.201655:   - Direction: 0
2025-08-26 15:14:42.201655:   - Antenna: 1
2025-08-26 15:14:42.202651:   - TagFrequency: 0
2025-08-26 15:14:42.202651: 🏷️ LSGate UID[1]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 51], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:42.202651:   - UID: E004015304F3DD22
2025-08-26 15:14:42.202651:   - Data: E004015304F3DD22
2025-08-26 15:14:42.202651:   - EventType: 1
2025-08-26 15:14:42.202651:   - Direction: 0
2025-08-26 15:14:42.203648:   - Antenna: 1
2025-08-26 15:14:42.203648:   - TagFrequency: 0
2025-08-26 15:14:42.692028: 🔄 开始RFID轮询检查...
2025-08-26 15:14:42.692028: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 15:14:42.692028: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 15:14:42.692028: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 15:14:42.693027: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 15:14:42.694021: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:42.694021:   - 设备句柄: 2528519287680
2025-08-26 15:14:42.694021:   - FetchRecords返回值: 0
2025-08-26 15:14:42.694021:   - 报告数量: 2
2025-08-26 15:14:42.695018:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:42.695018:   - 原始数据: 01 00 19 08 1A 0F 0F 2B 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:42.695018:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:42.695018:   - 设备类型: LSGControlCenter
2025-08-26 15:14:42.695018:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:42.695018:   - 数据长度: 8
2025-08-26 15:14:42.695018:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:42.696015:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:42.696015:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:42.696015:   - 原始数据: 01 00 19 08 1A 0F 0F 33 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:42.696015:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:42.696015:   - 设备类型: LSGControlCenter
2025-08-26 15:14:42.696015:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:42.696015:   - 数据长度: 8
2025-08-26 15:14:42.696015:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:42.697011:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:42.697011: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:42.697011:   - 发现标签数量: 2
2025-08-26 15:14:42.697011:   - 标签详情:
2025-08-26 15:14:42.697011:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:42.697011:     [1] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:42.697011: RFID扫描: 发现 2 个标签
2025-08-26 15:14:42.697011: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 15:14:42.698008: 🎉 LSGate检测到RFID标签！
2025-08-26 15:14:42.698008: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 43], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:42.698008:   - UID: E004015304F3DD22
2025-08-26 15:14:42.698008:   - Data: E004015304F3DD22
2025-08-26 15:14:42.698008:   - EventType: 1
2025-08-26 15:14:42.698008:   - Direction: 0
2025-08-26 15:14:42.698008:   - Antenna: 1
2025-08-26 15:14:42.698008:   - TagFrequency: 0
2025-08-26 15:14:42.699005: 🏷️ LSGate UID[1]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 51], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:42.699005:   - UID: E004015304F3DD22
2025-08-26 15:14:42.699005:   - Data: E004015304F3DD22
2025-08-26 15:14:42.699005:   - EventType: 1
2025-08-26 15:14:42.699005:   - Direction: 0
2025-08-26 15:14:42.699005:   - Antenna: 1
2025-08-26 15:14:42.699005:   - TagFrequency: 0
2025-08-26 15:14:43.191372: 🔄 开始RFID轮询检查...
2025-08-26 15:14:43.191372: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 15:14:43.191372: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 15:14:43.191372: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 15:14:43.192369: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 15:14:43.193366: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:43.193366:   - 设备句柄: 2528519287680
2025-08-26 15:14:43.193366:   - FetchRecords返回值: 0
2025-08-26 15:14:43.193366:   - 报告数量: 2
2025-08-26 15:14:43.193366:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:43.194362:   - 原始数据: 01 00 19 08 1A 0F 0F 2B 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:43.194362:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:43.194362:   - 设备类型: LSGControlCenter
2025-08-26 15:14:43.194362:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:43.194362:   - 数据长度: 8
2025-08-26 15:14:43.194362:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:43.194362:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:43.194362:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:43.195359:   - 原始数据: 01 00 19 08 1A 0F 0F 33 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:43.195359:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:43.195359:   - 设备类型: LSGControlCenter
2025-08-26 15:14:43.195359:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:43.195359:   - 数据长度: 8
2025-08-26 15:14:43.195359:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:43.195359:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:43.196356: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:43.196356:   - 发现标签数量: 2
2025-08-26 15:14:43.196356:   - 标签详情:
2025-08-26 15:14:43.196356:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:43.196356:     [1] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:43.196356: RFID扫描: 发现 2 个标签
2025-08-26 15:14:43.196356: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 15:14:43.196356: 🎉 LSGate检测到RFID标签！
2025-08-26 15:14:43.197352: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 43], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:43.197352:   - UID: E004015304F3DD22
2025-08-26 15:14:43.197352:   - Data: E004015304F3DD22
2025-08-26 15:14:43.197352:   - EventType: 1
2025-08-26 15:14:43.197352:   - Direction: 0
2025-08-26 15:14:43.197352:   - Antenna: 1
2025-08-26 15:14:43.197352:   - TagFrequency: 0
2025-08-26 15:14:43.197352: 🏷️ LSGate UID[1]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 51], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:43.198349:   - UID: E004015304F3DD22
2025-08-26 15:14:43.198349:   - Data: E004015304F3DD22
2025-08-26 15:14:43.198349:   - EventType: 1
2025-08-26 15:14:43.198349:   - Direction: 0
2025-08-26 15:14:43.198349:   - Antenna: 1
2025-08-26 15:14:43.198349:   - TagFrequency: 0
2025-08-26 15:14:43.691713: 🔄 开始RFID轮询检查...
2025-08-26 15:14:43.691713: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 15:14:43.691713: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 15:14:43.691713: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 15:14:43.692710: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 15:14:43.693706: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:43.693706:   - 设备句柄: 2528519287680
2025-08-26 15:14:43.693706:   - FetchRecords返回值: 0
2025-08-26 15:14:43.693706:   - 报告数量: 2
2025-08-26 15:14:43.694703:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:43.694703:   - 原始数据: 01 00 19 08 1A 0F 0F 2B 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:43.694703:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:43.694703:   - 设备类型: LSGControlCenter
2025-08-26 15:14:43.694703:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:43.694703:   - 数据长度: 8
2025-08-26 15:14:43.694703:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:43.695700:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:43.695700:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:43.695700:   - 原始数据: 01 00 19 08 1A 0F 0F 33 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:43.695700:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:43.695700:   - 设备类型: LSGControlCenter
2025-08-26 15:14:43.695700:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:43.695700:   - 数据长度: 8
2025-08-26 15:14:43.695700:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:43.696696:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:43.696696: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:43.696696:   - 发现标签数量: 2
2025-08-26 15:14:43.696696:   - 标签详情:
2025-08-26 15:14:43.696696:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:43.696696:     [1] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:43.696696: RFID扫描: 发现 2 个标签
2025-08-26 15:14:43.696696: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 15:14:43.697693: 🎉 LSGate检测到RFID标签！
2025-08-26 15:14:43.697693: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 43], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:43.697693:   - UID: E004015304F3DD22
2025-08-26 15:14:43.697693:   - Data: E004015304F3DD22
2025-08-26 15:14:43.697693:   - EventType: 1
2025-08-26 15:14:43.697693:   - Direction: 0
2025-08-26 15:14:43.697693:   - Antenna: 1
2025-08-26 15:14:43.697693:   - TagFrequency: 0
2025-08-26 15:14:43.698690: 🏷️ LSGate UID[1]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 51], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:43.698690:   - UID: E004015304F3DD22
2025-08-26 15:14:43.698690:   - Data: E004015304F3DD22
2025-08-26 15:14:43.698690:   - EventType: 1
2025-08-26 15:14:43.698690:   - Direction: 0
2025-08-26 15:14:43.698690:   - Antenna: 1
2025-08-26 15:14:43.698690:   - TagFrequency: 0
2025-08-26 15:14:44.191575: 🔄 开始RFID轮询检查...
2025-08-26 15:14:44.191575: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 15:14:44.191575: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 15:14:44.191575: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 15:14:44.192572: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 15:14:44.193568: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:44.193568:   - 设备句柄: 2528519287680
2025-08-26 15:14:44.193568:   - FetchRecords返回值: 0
2025-08-26 15:14:44.193568:   - 报告数量: 2
2025-08-26 15:14:44.193568:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:44.194565:   - 原始数据: 01 00 19 08 1A 0F 0F 2B 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:44.194565:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:44.194565:   - 设备类型: LSGControlCenter
2025-08-26 15:14:44.194565:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:44.194565:   - 数据长度: 8
2025-08-26 15:14:44.194565:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:44.194565:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:44.195561:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:44.195561:   - 原始数据: 01 00 19 08 1A 0F 0F 33 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:44.195561:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:44.195561:   - 设备类型: LSGControlCenter
2025-08-26 15:14:44.195561:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:44.195561:   - 数据长度: 8
2025-08-26 15:14:44.195561:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:44.195561:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:44.196558: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:44.196558:   - 发现标签数量: 2
2025-08-26 15:14:44.196558:   - 标签详情:
2025-08-26 15:14:44.196558:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:44.196558:     [1] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:44.196558: RFID扫描: 发现 2 个标签
2025-08-26 15:14:44.196558: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 15:14:44.196558: 🎉 LSGate检测到RFID标签！
2025-08-26 15:14:44.197555: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 43], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:44.197555:   - UID: E004015304F3DD22
2025-08-26 15:14:44.197555:   - Data: E004015304F3DD22
2025-08-26 15:14:44.197555:   - EventType: 1
2025-08-26 15:14:44.197555:   - Direction: 0
2025-08-26 15:14:44.197555:   - Antenna: 1
2025-08-26 15:14:44.197555:   - TagFrequency: 0
2025-08-26 15:14:44.198552: 🏷️ LSGate UID[1]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 51], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:44.198552:   - UID: E004015304F3DD22
2025-08-26 15:14:44.198552:   - Data: E004015304F3DD22
2025-08-26 15:14:44.198552:   - EventType: 1
2025-08-26 15:14:44.198552:   - Direction: 0
2025-08-26 15:14:44.198552:   - Antenna: 1
2025-08-26 15:14:44.198552:   - TagFrequency: 0
2025-08-26 15:14:44.573309: 模拟出馆完成，状态重置为idle
2025-08-26 15:14:44.573309: 闸机状态变更: GateState.exitOver -> GateState.idle
2025-08-26 15:14:44.573309: 闸机状态更新: GateState.exitOver -> GateState.idle
2025-08-26 15:14:44.573309: [channel_1] 收到闸机事件: state_changed
2025-08-26 15:14:44.574305: 📨 收到GateCoordinator事件: state_changed
2025-08-26 15:14:44.574305: 闸机状态变更: GateState.idle
2025-08-26 15:14:44.574305: 🎨 处理状态变更UI: idle
2025-08-26 15:14:44.574305: 页面状态变更: SilencePageState.welcome
2025-08-26 15:14:44.691916: 🔄 开始RFID轮询检查...
2025-08-26 15:14:44.691916: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 15:14:44.691916: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 15:14:44.691916: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 15:14:44.691916: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 15:14:44.693909: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:44.693909:   - 设备句柄: 2528519287680
2025-08-26 15:14:44.694909:   - FetchRecords返回值: 0
2025-08-26 15:14:44.694909:   - 报告数量: 2
2025-08-26 15:14:44.694909:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:44.694909:   - 原始数据: 01 00 19 08 1A 0F 0F 2B 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:44.694909:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:44.695902:   - 设备类型: LSGControlCenter
2025-08-26 15:14:44.695902:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:44.695902:   - 数据长度: 8
2025-08-26 15:14:44.695902:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:44.696900:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:44.696900:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:44.696900:   - 原始数据: 01 00 19 08 1A 0F 0F 33 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:44.696900:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:44.696900:   - 设备类型: LSGControlCenter
2025-08-26 15:14:44.697896:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:44.697896:   - 数据长度: 8
2025-08-26 15:14:44.697896:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:44.697896:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:44.698897: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:44.698897:   - 发现标签数量: 2
2025-08-26 15:14:44.698897:   - 标签详情:
2025-08-26 15:14:44.699893:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:44.699893:     [1] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:44.699893: RFID扫描: 发现 2 个标签
2025-08-26 15:14:44.699893: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 15:14:44.699893: 🎉 LSGate检测到RFID标签！
2025-08-26 15:14:44.700886: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 43], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:44.700886:   - UID: E004015304F3DD22
2025-08-26 15:14:44.700886:   - Data: E004015304F3DD22
2025-08-26 15:14:44.700886:   - EventType: 1
2025-08-26 15:14:44.700886:   - Direction: 0
2025-08-26 15:14:44.700886:   - Antenna: 1
2025-08-26 15:14:44.700886:   - TagFrequency: 0
2025-08-26 15:14:44.701882: 🏷️ LSGate UID[1]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 51], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:44.701882:   - UID: E004015304F3DD22
2025-08-26 15:14:44.701882:   - Data: E004015304F3DD22
2025-08-26 15:14:44.701882:   - EventType: 1
2025-08-26 15:14:44.701882:   - Direction: 0
2025-08-26 15:14:44.701882:   - Antenna: 1
2025-08-26 15:14:44.701882:   - TagFrequency: 0
2025-08-26 15:14:45.191794: 🔄 开始RFID轮询检查...
2025-08-26 15:14:45.192790: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 15:14:45.192790: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 15:14:45.193788: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 15:14:45.193788: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 15:14:45.193788: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:45.193788:   - 设备句柄: 2528519287680
2025-08-26 15:14:45.193788:   - FetchRecords返回值: 0
2025-08-26 15:14:45.194783:   - 报告数量: 2
2025-08-26 15:14:45.194783:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:45.194783:   - 原始数据: 01 00 19 08 1A 0F 0F 2B 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:45.194783:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:45.194783:   - 设备类型: LSGControlCenter
2025-08-26 15:14:45.194783:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:45.194783:   - 数据长度: 8
2025-08-26 15:14:45.195779:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:45.195779:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:45.195779:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:45.195779:   - 原始数据: 01 00 19 08 1A 0F 0F 33 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:45.195779:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:45.195779:   - 设备类型: LSGControlCenter
2025-08-26 15:14:45.195779:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:45.196776:   - 数据长度: 8
2025-08-26 15:14:45.196776:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:45.196776:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:45.196776: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:45.196776:   - 发现标签数量: 2
2025-08-26 15:14:45.196776:   - 标签详情:
2025-08-26 15:14:45.196776:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:45.196776:     [1] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:45.197772: RFID扫描: 发现 2 个标签
2025-08-26 15:14:45.197772: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 15:14:45.197772: 🎉 LSGate检测到RFID标签！
2025-08-26 15:14:45.197772: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 43], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:45.197772:   - UID: E004015304F3DD22
2025-08-26 15:14:45.197772:   - Data: E004015304F3DD22
2025-08-26 15:14:45.197772:   - EventType: 1
2025-08-26 15:14:45.197772:   - Direction: 0
2025-08-26 15:14:45.198769:   - Antenna: 1
2025-08-26 15:14:45.198769:   - TagFrequency: 0
2025-08-26 15:14:45.198769: 🏷️ LSGate UID[1]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 51], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:45.198769:   - UID: E004015304F3DD22
2025-08-26 15:14:45.198769:   - Data: E004015304F3DD22
2025-08-26 15:14:45.198769:   - EventType: 1
2025-08-26 15:14:45.198769:   - Direction: 0
2025-08-26 15:14:45.198769:   - Antenna: 1
2025-08-26 15:14:45.199766:   - TagFrequency: 0
2025-08-26 15:14:45.692133: 🔄 开始RFID轮询检查...
2025-08-26 15:14:45.692133: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 15:14:45.692133: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 15:14:45.692133: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 15:14:45.692133: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 15:14:45.694127: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:45.694127:   - 设备句柄: 2528519287680
2025-08-26 15:14:45.694127:   - FetchRecords返回值: 0
2025-08-26 15:14:45.694127:   - 报告数量: 2
2025-08-26 15:14:45.695124:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:45.695124:   - 原始数据: 01 00 19 08 1A 0F 0F 2B 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:45.695124:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:45.695124:   - 设备类型: LSGControlCenter
2025-08-26 15:14:45.695124:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:45.695124:   - 数据长度: 8
2025-08-26 15:14:45.695124:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:45.696120:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:45.696120:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:45.696120:   - 原始数据: 01 00 19 08 1A 0F 0F 33 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:45.696120:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:45.696120:   - 设备类型: LSGControlCenter
2025-08-26 15:14:45.696120:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:45.696120:   - 数据长度: 8
2025-08-26 15:14:45.696120:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:45.697116:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:45.697116: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:45.697116:   - 发现标签数量: 2
2025-08-26 15:14:45.697116:   - 标签详情:
2025-08-26 15:14:45.697116:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:45.697116:     [1] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:45.697116: RFID扫描: 发现 2 个标签
2025-08-26 15:14:45.698113: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 15:14:45.698113: 🎉 LSGate检测到RFID标签！
2025-08-26 15:14:45.698113: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 43], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:45.698113:   - UID: E004015304F3DD22
2025-08-26 15:14:45.698113:   - Data: E004015304F3DD22
2025-08-26 15:14:45.698113:   - EventType: 1
2025-08-26 15:14:45.698113:   - Direction: 0
2025-08-26 15:14:45.698113:   - Antenna: 1
2025-08-26 15:14:45.699110:   - TagFrequency: 0
2025-08-26 15:14:45.699110: 🏷️ LSGate UID[1]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 51], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:45.699110:   - UID: E004015304F3DD22
2025-08-26 15:14:45.699110:   - Data: E004015304F3DD22
2025-08-26 15:14:45.699110:   - EventType: 1
2025-08-26 15:14:45.699110:   - Direction: 0
2025-08-26 15:14:45.699110:   - Antenna: 1
2025-08-26 15:14:45.699110:   - TagFrequency: 0
2025-08-26 15:14:46.191477: 🔄 开始RFID轮询检查...
2025-08-26 15:14:46.191477: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 15:14:46.191477: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 15:14:46.191477: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 15:14:46.191477: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 15:14:46.193479: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:46.193479:   - 设备句柄: 2528519287680
2025-08-26 15:14:46.194468:   - FetchRecords返回值: 0
2025-08-26 15:14:46.194468:   - 报告数量: 2
2025-08-26 15:14:46.194468:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:46.194468:   - 原始数据: 01 00 19 08 1A 0F 0F 2B 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:46.194468:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:46.194468:   - 设备类型: LSGControlCenter
2025-08-26 15:14:46.195465:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:46.195465:   - 数据长度: 8
2025-08-26 15:14:46.195465:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:46.195465:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:46.195465:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:46.196464:   - 原始数据: 01 00 19 08 1A 0F 0F 33 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:46.196464:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:46.196464:   - 设备类型: LSGControlCenter
2025-08-26 15:14:46.197460:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:46.197460:   - 数据长度: 8
2025-08-26 15:14:46.197460:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:46.197460:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:46.198455: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:46.198455:   - 发现标签数量: 2
2025-08-26 15:14:46.198455:   - 标签详情:
2025-08-26 15:14:46.198455:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:46.198455:     [1] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:46.198455: RFID扫描: 发现 2 个标签
2025-08-26 15:14:46.198455: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 15:14:46.199451: 🎉 LSGate检测到RFID标签！
2025-08-26 15:14:46.199451: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 43], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:46.199451:   - UID: E004015304F3DD22
2025-08-26 15:14:46.199451:   - Data: E004015304F3DD22
2025-08-26 15:14:46.199451:   - EventType: 1
2025-08-26 15:14:46.199451:   - Direction: 0
2025-08-26 15:14:46.199451:   - Antenna: 1
2025-08-26 15:14:46.200448:   - TagFrequency: 0
2025-08-26 15:14:46.200448: 🏷️ LSGate UID[1]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 51], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:46.200448:   - UID: E004015304F3DD22
2025-08-26 15:14:46.200448:   - Data: E004015304F3DD22
2025-08-26 15:14:46.200448:   - EventType: 1
2025-08-26 15:14:46.200448:   - Direction: 0
2025-08-26 15:14:46.200448:   - Antenna: 1
2025-08-26 15:14:46.200448:   - TagFrequency: 0
2025-08-26 15:14:46.691818: 🔄 开始RFID轮询检查...
2025-08-26 15:14:46.691818: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 15:14:46.691818: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 15:14:46.691818: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 15:14:46.692815: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 15:14:46.693812: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:46.693812:   - 设备句柄: 2528519287680
2025-08-26 15:14:46.693812:   - FetchRecords返回值: 0
2025-08-26 15:14:46.693812:   - 报告数量: 2
2025-08-26 15:14:46.694808:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:46.694808:   - 原始数据: 01 00 19 08 1A 0F 0F 2B 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:46.694808:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:46.694808:   - 设备类型: LSGControlCenter
2025-08-26 15:14:46.694808:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:46.694808:   - 数据长度: 8
2025-08-26 15:14:46.694808:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:46.695805:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:46.695805:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:46.695805:   - 原始数据: 01 00 19 08 1A 0F 0F 33 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:46.695805:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:46.695805:   - 设备类型: LSGControlCenter
2025-08-26 15:14:46.695805:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:46.695805:   - 数据长度: 8
2025-08-26 15:14:46.695805:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:46.696802:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:46.696802: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:46.696802:   - 发现标签数量: 2
2025-08-26 15:14:46.696802:   - 标签详情:
2025-08-26 15:14:46.696802:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:46.696802:     [1] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:46.696802: RFID扫描: 发现 2 个标签
2025-08-26 15:14:46.696802: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 15:14:46.697798: 🎉 LSGate检测到RFID标签！
2025-08-26 15:14:46.697798: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 43], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:46.697798:   - UID: E004015304F3DD22
2025-08-26 15:14:46.697798:   - Data: E004015304F3DD22
2025-08-26 15:14:46.697798:   - EventType: 1
2025-08-26 15:14:46.697798:   - Direction: 0
2025-08-26 15:14:46.697798:   - Antenna: 1
2025-08-26 15:14:46.698795:   - TagFrequency: 0
2025-08-26 15:14:46.698795: 🏷️ LSGate UID[1]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 51], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:46.698795:   - UID: E004015304F3DD22
2025-08-26 15:14:46.698795:   - Data: E004015304F3DD22
2025-08-26 15:14:46.698795:   - EventType: 1
2025-08-26 15:14:46.698795:   - Direction: 0
2025-08-26 15:14:46.698795:   - Antenna: 1
2025-08-26 15:14:46.698795:   - TagFrequency: 0
2025-08-26 15:14:47.191679: 🔄 开始RFID轮询检查...
2025-08-26 15:14:47.191679: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 15:14:47.191679: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 15:14:47.191679: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 15:14:47.192677: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 15:14:47.193673: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:47.193673:   - 设备句柄: 2528519287680
2025-08-26 15:14:47.193673:   - FetchRecords返回值: 0
2025-08-26 15:14:47.194670:   - 报告数量: 2
2025-08-26 15:14:47.194670:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:47.194670:   - 原始数据: 01 00 19 08 1A 0F 0F 2B 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:47.194670:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:47.194670:   - 设备类型: LSGControlCenter
2025-08-26 15:14:47.194670:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:47.194670:   - 数据长度: 8
2025-08-26 15:14:47.194670:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:47.195668:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:47.195668:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:47.195668:   - 原始数据: 01 00 19 08 1A 0F 0F 33 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:47.195668:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:47.195668:   - 设备类型: LSGControlCenter
2025-08-26 15:14:47.195668:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:47.195668:   - 数据长度: 8
2025-08-26 15:14:47.195668:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:47.196663:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:47.196663: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:47.196663:   - 发现标签数量: 2
2025-08-26 15:14:47.196663:   - 标签详情:
2025-08-26 15:14:47.196663:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:47.196663:     [1] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:47.196663: RFID扫描: 发现 2 个标签
2025-08-26 15:14:47.196663: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 15:14:47.197660: 🎉 LSGate检测到RFID标签！
2025-08-26 15:14:47.197660: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 43], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:47.197660:   - UID: E004015304F3DD22
2025-08-26 15:14:47.197660:   - Data: E004015304F3DD22
2025-08-26 15:14:47.197660:   - EventType: 1
2025-08-26 15:14:47.197660:   - Direction: 0
2025-08-26 15:14:47.197660:   - Antenna: 1
2025-08-26 15:14:47.198656:   - TagFrequency: 0
2025-08-26 15:14:47.198656: 🏷️ LSGate UID[1]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 51], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:47.198656:   - UID: E004015304F3DD22
2025-08-26 15:14:47.198656:   - Data: E004015304F3DD22
2025-08-26 15:14:47.198656:   - EventType: 1
2025-08-26 15:14:47.198656:   - Direction: 0
2025-08-26 15:14:47.198656:   - Antenna: 1
2025-08-26 15:14:47.198656:   - TagFrequency: 0
2025-08-26 15:14:47.692020: 🔄 开始RFID轮询检查...
2025-08-26 15:14:47.692020: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 15:14:47.692020: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 15:14:47.693025: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 15:14:47.693025: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 15:14:47.694014: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:47.694014:   - 设备句柄: 2528519287680
2025-08-26 15:14:47.694014:   - FetchRecords返回值: 0
2025-08-26 15:14:47.694014:   - 报告数量: 2
2025-08-26 15:14:47.694014:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:47.695010:   - 原始数据: 01 00 19 08 1A 0F 0F 2B 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:47.695010:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:47.695010:   - 设备类型: LSGControlCenter
2025-08-26 15:14:47.695010:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:47.695010:   - 数据长度: 8
2025-08-26 15:14:47.695010:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:47.695010:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:47.695010:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:47.696007:   - 原始数据: 01 00 19 08 1A 0F 0F 33 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:47.696007:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:47.696007:   - 设备类型: LSGControlCenter
2025-08-26 15:14:47.696007:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:47.696007:   - 数据长度: 8
2025-08-26 15:14:47.696007:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:47.697004:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:47.697004: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:47.697004:   - 发现标签数量: 2
2025-08-26 15:14:47.697004:   - 标签详情:
2025-08-26 15:14:47.697004:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:47.697004:     [1] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:47.697004: RFID扫描: 发现 2 个标签
2025-08-26 15:14:47.697004: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 15:14:47.698: 🎉 LSGate检测到RFID标签！
2025-08-26 15:14:47.698: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 43], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:47.698:   - UID: E004015304F3DD22
2025-08-26 15:14:47.698:   - Data: E004015304F3DD22
2025-08-26 15:14:47.698:   - EventType: 1
2025-08-26 15:14:47.698:   - Direction: 0
2025-08-26 15:14:47.698:   - Antenna: 1
2025-08-26 15:14:47.698:   - TagFrequency: 0
2025-08-26 15:14:47.698997: 🏷️ LSGate UID[1]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 51], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:47.698997:   - UID: E004015304F3DD22
2025-08-26 15:14:47.698997:   - Data: E004015304F3DD22
2025-08-26 15:14:47.698997:   - EventType: 1
2025-08-26 15:14:47.698997:   - Direction: 0
2025-08-26 15:14:47.698997:   - Antenna: 1
2025-08-26 15:14:47.698997:   - TagFrequency: 0
2025-08-26 15:14:48.191364: 🔄 开始RFID轮询检查...
2025-08-26 15:14:48.191364: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 15:14:48.191364: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 15:14:48.191364: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 15:14:48.192361: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 15:14:48.193357: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:48.193357:   - 设备句柄: 2528519287680
2025-08-26 15:14:48.193357:   - FetchRecords返回值: 0
2025-08-26 15:14:48.193357:   - 报告数量: 2
2025-08-26 15:14:48.193357:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:48.194354:   - 原始数据: 01 00 19 08 1A 0F 0F 2B 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:48.194354:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:48.194354:   - 设备类型: LSGControlCenter
2025-08-26 15:14:48.194354:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:48.194354:   - 数据长度: 8
2025-08-26 15:14:48.194354:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:48.194354:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:48.194354:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:48.195351:   - 原始数据: 01 00 19 08 1A 0F 0F 33 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:48.195351:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:48.195351:   - 设备类型: LSGControlCenter
2025-08-26 15:14:48.195351:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:48.195351:   - 数据长度: 8
2025-08-26 15:14:48.195351:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:48.195351:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:48.195351: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:48.196348:   - 发现标签数量: 2
2025-08-26 15:14:48.196348:   - 标签详情:
2025-08-26 15:14:48.196348:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:48.196348:     [1] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:48.196348: RFID扫描: 发现 2 个标签
2025-08-26 15:14:48.196348: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 15:14:48.196348: 🎉 LSGate检测到RFID标签！
2025-08-26 15:14:48.197344: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 43], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:48.197344:   - UID: E004015304F3DD22
2025-08-26 15:14:48.197344:   - Data: E004015304F3DD22
2025-08-26 15:14:48.197344:   - EventType: 1
2025-08-26 15:14:48.197344:   - Direction: 0
2025-08-26 15:14:48.197344:   - Antenna: 1
2025-08-26 15:14:48.197344:   - TagFrequency: 0
2025-08-26 15:14:48.197344: 🏷️ LSGate UID[1]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 51], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:48.197344:   - UID: E004015304F3DD22
2025-08-26 15:14:48.198341:   - Data: E004015304F3DD22
2025-08-26 15:14:48.198341:   - EventType: 1
2025-08-26 15:14:48.198341:   - Direction: 0
2025-08-26 15:14:48.198341:   - Antenna: 1
2025-08-26 15:14:48.198341:   - TagFrequency: 0
2025-08-26 15:14:48.692567: 🔄 开始RFID轮询检查...
2025-08-26 15:14:48.692567: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 15:14:48.692567: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 15:14:48.692567: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 15:14:48.693564: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 15:14:48.694561: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:48.694561:   - 设备句柄: 2528519287680
2025-08-26 15:14:48.694561:   - FetchRecords返回值: 0
2025-08-26 15:14:48.694561:   - 报告数量: 2
2025-08-26 15:14:48.695558:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:48.695558:   - 原始数据: 01 00 19 08 1A 0F 0F 2B 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:48.695558:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:48.695558:   - 设备类型: LSGControlCenter
2025-08-26 15:14:48.695558:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:48.695558:   - 数据长度: 8
2025-08-26 15:14:48.695558:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:48.695558:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:48.696554:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:48.696554:   - 原始数据: 01 00 19 08 1A 0F 0F 33 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:48.696554:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:48.696554:   - 设备类型: LSGControlCenter
2025-08-26 15:14:48.696554:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:48.696554:   - 数据长度: 8
2025-08-26 15:14:48.696554:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:48.697551:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:48.697551: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:48.697551:   - 发现标签数量: 2
2025-08-26 15:14:48.697551:   - 标签详情:
2025-08-26 15:14:48.697551:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:48.697551:     [1] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:48.697551: RFID扫描: 发现 2 个标签
2025-08-26 15:14:48.697551: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 15:14:48.698548: 🎉 LSGate检测到RFID标签！
2025-08-26 15:14:48.698548: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 43], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:48.698548:   - UID: E004015304F3DD22
2025-08-26 15:14:48.698548:   - Data: E004015304F3DD22
2025-08-26 15:14:48.698548:   - EventType: 1
2025-08-26 15:14:48.698548:   - Direction: 0
2025-08-26 15:14:48.698548:   - Antenna: 1
2025-08-26 15:14:48.698548:   - TagFrequency: 0
2025-08-26 15:14:48.699544: 🏷️ LSGate UID[1]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 51], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:48.699544:   - UID: E004015304F3DD22
2025-08-26 15:14:48.699544:   - Data: E004015304F3DD22
2025-08-26 15:14:48.699544:   - EventType: 1
2025-08-26 15:14:48.699544:   - Direction: 0
2025-08-26 15:14:48.699544:   - Antenna: 1
2025-08-26 15:14:48.699544:   - TagFrequency: 0
2025-08-26 15:14:49.191912: 🔄 开始RFID轮询检查...
2025-08-26 15:14:49.191912: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 15:14:49.191912: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 15:14:49.191912: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 15:14:49.191912: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 15:14:49.193905: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:49.193905:   - 设备句柄: 2528519287680
2025-08-26 15:14:49.193905:   - FetchRecords返回值: 0
2025-08-26 15:14:49.193905:   - 报告数量: 2
2025-08-26 15:14:49.194902:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:49.194902:   - 原始数据: 01 00 19 08 1A 0F 0F 2B 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:49.194902:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:49.194902:   - 设备类型: LSGControlCenter
2025-08-26 15:14:49.194902:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:49.194902:   - 数据长度: 8
2025-08-26 15:14:49.194902:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:49.195898:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:49.195898:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:49.195898:   - 原始数据: 01 00 19 08 1A 0F 0F 33 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:49.195898:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:49.195898:   - 设备类型: LSGControlCenter
2025-08-26 15:14:49.195898:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:49.195898:   - 数据长度: 8
2025-08-26 15:14:49.195898:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:49.196895:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:49.196895: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:49.196895:   - 发现标签数量: 2
2025-08-26 15:14:49.196895:   - 标签详情:
2025-08-26 15:14:49.196895:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:49.196895:     [1] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:49.196895: RFID扫描: 发现 2 个标签
2025-08-26 15:14:49.196895: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 15:14:49.197892: 🎉 LSGate检测到RFID标签！
2025-08-26 15:14:49.197892: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 43], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:49.197892:   - UID: E004015304F3DD22
2025-08-26 15:14:49.197892:   - Data: E004015304F3DD22
2025-08-26 15:14:49.197892:   - EventType: 1
2025-08-26 15:14:49.197892:   - Direction: 0
2025-08-26 15:14:49.197892:   - Antenna: 1
2025-08-26 15:14:49.198889:   - TagFrequency: 0
2025-08-26 15:14:49.198889: 🏷️ LSGate UID[1]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 51], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:49.198889:   - UID: E004015304F3DD22
2025-08-26 15:14:49.198889:   - Data: E004015304F3DD22
2025-08-26 15:14:49.198889:   - EventType: 1
2025-08-26 15:14:49.198889:   - Direction: 0
2025-08-26 15:14:49.198889:   - Antenna: 1
2025-08-26 15:14:49.198889:   - TagFrequency: 0
2025-08-26 15:14:49.692252: 🔄 开始RFID轮询检查...
2025-08-26 15:14:49.692252: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 15:14:49.692252: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 15:14:49.692252: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 15:14:49.692252: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 15:14:49.694246: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:49.694246:   - 设备句柄: 2528519287680
2025-08-26 15:14:49.694246:   - FetchRecords返回值: 0
2025-08-26 15:14:49.694246:   - 报告数量: 2
2025-08-26 15:14:49.695242:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:49.695242:   - 原始数据: 01 00 19 08 1A 0F 0F 2B 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:49.695242:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:49.695242:   - 设备类型: LSGControlCenter
2025-08-26 15:14:49.695242:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:49.695242:   - 数据长度: 8
2025-08-26 15:14:49.695242:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:49.696239:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:49.696239:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:49.696239:   - 原始数据: 01 00 19 08 1A 0F 0F 33 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:49.696239:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:49.696239:   - 设备类型: LSGControlCenter
2025-08-26 15:14:49.696239:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:49.696239:   - 数据长度: 8
2025-08-26 15:14:49.696239:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:49.697236:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:49.697236: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:49.697236:   - 发现标签数量: 2
2025-08-26 15:14:49.697236:   - 标签详情:
2025-08-26 15:14:49.697236:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:49.697236:     [1] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:49.697236: RFID扫描: 发现 2 个标签
2025-08-26 15:14:49.697236: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 15:14:49.698232: 🎉 LSGate检测到RFID标签！
2025-08-26 15:14:49.698232: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 43], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:49.698232:   - UID: E004015304F3DD22
2025-08-26 15:14:49.698232:   - Data: E004015304F3DD22
2025-08-26 15:14:49.698232:   - EventType: 1
2025-08-26 15:14:49.698232:   - Direction: 0
2025-08-26 15:14:49.698232:   - Antenna: 1
2025-08-26 15:14:49.698232:   - TagFrequency: 0
2025-08-26 15:14:49.699229: 🏷️ LSGate UID[1]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 51], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:49.699229:   - UID: E004015304F3DD22
2025-08-26 15:14:49.699229:   - Data: E004015304F3DD22
2025-08-26 15:14:49.699229:   - EventType: 1
2025-08-26 15:14:49.699229:   - Direction: 0
2025-08-26 15:14:49.699229:   - Antenna: 1
2025-08-26 15:14:49.699229:   - TagFrequency: 0
2025-08-26 15:14:50.192116: 🔄 开始RFID轮询检查...
2025-08-26 15:14:50.192116: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 15:14:50.192116: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 15:14:50.192116: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 15:14:50.192116: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 15:14:50.194109: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:50.194109:   - 设备句柄: 2528519287680
2025-08-26 15:14:50.194109:   - FetchRecords返回值: 0
2025-08-26 15:14:50.194109:   - 报告数量: 2
2025-08-26 15:14:50.195106:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:50.195106:   - 原始数据: 01 00 19 08 1A 0F 0F 2B 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:50.195106:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:50.195106:   - 设备类型: LSGControlCenter
2025-08-26 15:14:50.195106:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:50.195106:   - 数据长度: 8
2025-08-26 15:14:50.195106:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:50.195106:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:50.196103:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:50.196103:   - 原始数据: 01 00 19 08 1A 0F 0F 33 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:50.196103:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:50.196103:   - 设备类型: LSGControlCenter
2025-08-26 15:14:50.196103:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:50.196103:   - 数据长度: 8
2025-08-26 15:14:50.196103:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:50.197099:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:50.197099: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:50.197099:   - 发现标签数量: 2
2025-08-26 15:14:50.197099:   - 标签详情:
2025-08-26 15:14:50.197099:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:50.197099:     [1] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:50.197099: RFID扫描: 发现 2 个标签
2025-08-26 15:14:50.197099: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 15:14:50.198096: 🎉 LSGate检测到RFID标签！
2025-08-26 15:14:50.198096: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 43], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:50.198096:   - UID: E004015304F3DD22
2025-08-26 15:14:50.198096:   - Data: E004015304F3DD22
2025-08-26 15:14:50.198096:   - EventType: 1
2025-08-26 15:14:50.198096:   - Direction: 0
2025-08-26 15:14:50.198096:   - Antenna: 1
2025-08-26 15:14:50.198096:   - TagFrequency: 0
2025-08-26 15:14:50.199093: 🏷️ LSGate UID[1]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 51], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:50.199093:   - UID: E004015304F3DD22
2025-08-26 15:14:50.199093:   - Data: E004015304F3DD22
2025-08-26 15:14:50.199093:   - EventType: 1
2025-08-26 15:14:50.199093:   - Direction: 0
2025-08-26 15:14:50.199093:   - Antenna: 1
2025-08-26 15:14:50.199093:   - TagFrequency: 0
2025-08-26 15:14:50.692457: 🔄 开始RFID轮询检查...
2025-08-26 15:14:50.692457: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 15:14:50.693130: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 15:14:50.693130: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 15:14:50.693454: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 15:14:50.694452: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:50.694452:   - 设备句柄: 2528519287680
2025-08-26 15:14:50.694452:   - FetchRecords返回值: 0
2025-08-26 15:14:50.695448:   - 报告数量: 2
2025-08-26 15:14:50.695448:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:50.695448:   - 原始数据: 01 00 19 08 1A 0F 0F 2B 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:50.695448:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:50.695448:   - 设备类型: LSGControlCenter
2025-08-26 15:14:50.695448:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:50.695448:   - 数据长度: 8
2025-08-26 15:14:50.696444:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:50.696444:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:50.696444:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:50.696444:   - 原始数据: 01 00 19 08 1A 0F 0F 33 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:50.696444:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:50.696444:   - 设备类型: LSGControlCenter
2025-08-26 15:14:50.697440:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:50.697440:   - 数据长度: 8
2025-08-26 15:14:50.697440:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:50.697440:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:50.697440: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:50.697440:   - 发现标签数量: 2
2025-08-26 15:14:50.698437:   - 标签详情:
2025-08-26 15:14:50.698437:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:50.698437:     [1] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:50.698437: RFID扫描: 发现 2 个标签
2025-08-26 15:14:50.698437: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 15:14:50.698437: 🎉 LSGate检测到RFID标签！
2025-08-26 15:14:50.699434: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 43], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:50.699434:   - UID: E004015304F3DD22
2025-08-26 15:14:50.699434:   - Data: E004015304F3DD22
2025-08-26 15:14:50.699434:   - EventType: 1
2025-08-26 15:14:50.699434:   - Direction: 0
2025-08-26 15:14:50.699434:   - Antenna: 1
2025-08-26 15:14:50.699434:   - TagFrequency: 0
2025-08-26 15:14:50.700430: 🏷️ LSGate UID[1]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 51], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:50.700430:   - UID: E004015304F3DD22
2025-08-26 15:14:50.700430:   - Data: E004015304F3DD22
2025-08-26 15:14:50.700430:   - EventType: 1
2025-08-26 15:14:50.700430:   - Direction: 0
2025-08-26 15:14:50.700430:   - Antenna: 1
2025-08-26 15:14:50.700430:   - TagFrequency: 0
2025-08-26 15:14:50.974523: 接收到数据: aa 00 c9 80 00 00 26 7e
2025-08-26 15:14:50.974523: 🔍 接收到串口数据: aa 00 c9 80 00 00 26 7e
2025-08-26 15:14:50.974523: 🔍 数据长度: 8 字节
2025-08-26 15:14:50.974523: 🔍 预定义命令列表:
2025-08-26 15:14:50.974523:   GateCommand.enterStart: aa 00 64 80 00 00 06 d2
2025-08-26 15:14:50.974523:   GateCommand.enterEnd: aa 00 65 80 00 00 07 2e
2025-08-26 15:14:50.975519:   GateCommand.exitStart: aa 00 c8 80 00 00 27 82
2025-08-26 15:14:50.975519:   GateCommand.exitEnd: aa 00 c9 80 00 00 26 7e
2025-08-26 15:14:50.975519:   GateCommand.reachPosition: aa 00 0a 80 00 00
2025-08-26 15:14:50.975519:   GateCommand.enterOpen: aa 00 02 01 00 00 48 72
2025-08-26 15:14:50.975519:   GateCommand.exitOpen: aa 00 01 01 00 00 48 36
2025-08-26 15:14:50.975519:   GateCommand.failSignal: aa 00 02 01 00 00 48 72
2025-08-26 15:14:50.975519:   GateCommand.tailgating: aa 00 0f 80 00 00
2025-08-26 15:14:50.975519:   GateCommand.doorHasPerson: aa 00 0b 80 00 00
2025-08-26 15:14:50.975519: ✅ 解析到闸机命令: GateCommand.exitEnd
2025-08-26 15:14:50.976516: 解析到闸机命令: exit_end (出馆结束)
2025-08-26 15:14:50.976516: 收到闸机命令: exit_end (出馆结束)
2025-08-26 15:14:50.976516: 出馆流程结束
2025-08-26 15:14:50.976516: 📊 流程状态已清除：进馆=false, 出馆=false
2025-08-26 15:14:50.976516: [channel_1] 收到闸机事件: exit_end
2025-08-26 15:14:50.976516: [channel_1] 主从机扩展：处理出馆结束
2025-08-26 15:14:50.976516: [channel_1] 清空处理队列，当前大小: 0
2025-08-26 15:14:50.977512: [channel_1] 处理队列已清空
2025-08-26 15:14:50.977512: 📨 收到GateCoordinator事件: exit_end
2025-08-26 15:14:50.977512: 页面状态变更: SilencePageState.welcome
2025-08-26 15:14:50.977512: [channel_1] 通知收集到的条码: []
2025-08-26 15:14:50.977512: ✅ [channel_1] 数据流通知发送成功: 0个条码
2025-08-26 15:14:51.191802: 🔄 开始RFID轮询检查...
2025-08-26 15:14:51.191802: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 15:14:51.191802: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 15:14:51.192798: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 15:14:51.192798: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 15:14:51.193795: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:51.193795:   - 设备句柄: 2528519287680
2025-08-26 15:14:51.193795:   - FetchRecords返回值: 0
2025-08-26 15:14:51.194792:   - 报告数量: 2
2025-08-26 15:14:51.194792:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:51.194792:   - 原始数据: 01 00 19 08 1A 0F 0F 2B 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:51.194792:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:51.194792:   - 设备类型: LSGControlCenter
2025-08-26 15:14:51.194792:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:51.195788:   - 数据长度: 8
2025-08-26 15:14:51.195788:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:51.195788:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:51.195788:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:51.195788:   - 原始数据: 01 00 19 08 1A 0F 0F 33 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:51.195788:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:51.195788:   - 设备类型: LSGControlCenter
2025-08-26 15:14:51.196785:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:51.196785:   - 数据长度: 8
2025-08-26 15:14:51.196785:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:51.196785:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:51.196785: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:51.196785:   - 发现标签数量: 2
2025-08-26 15:14:51.196785:   - 标签详情:
2025-08-26 15:14:51.197782:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:51.197782:     [1] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:51.197782: RFID扫描: 发现 2 个标签
2025-08-26 15:14:51.197782: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 15:14:51.197782: 🎉 LSGate检测到RFID标签！
2025-08-26 15:14:51.197782: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 43], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:51.197782:   - UID: E004015304F3DD22
2025-08-26 15:14:51.197782:   - Data: E004015304F3DD22
2025-08-26 15:14:51.198778:   - EventType: 1
2025-08-26 15:14:51.198778:   - Direction: 0
2025-08-26 15:14:51.198778:   - Antenna: 1
2025-08-26 15:14:51.198778:   - TagFrequency: 0
2025-08-26 15:14:51.198778: 🏷️ LSGate UID[1]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 51], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:51.198778:   - UID: E004015304F3DD22
2025-08-26 15:14:51.198778:   - Data: E004015304F3DD22
2025-08-26 15:14:51.199775:   - EventType: 1
2025-08-26 15:14:51.199775:   - Direction: 0
2025-08-26 15:14:51.199775:   - Antenna: 1
2025-08-26 15:14:51.199775:   - TagFrequency: 0
2025-08-26 15:14:51.691145: 🔄 开始RFID轮询检查...
2025-08-26 15:14:51.691145: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 15:14:51.691145: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 15:14:51.691145: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 15:14:51.691145: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 15:14:51.693140: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:51.693140:   - 设备句柄: 2528519287680
2025-08-26 15:14:51.693140:   - FetchRecords返回值: 0
2025-08-26 15:14:51.694136:   - 报告数量: 2
2025-08-26 15:14:51.694136:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:51.694136:   - 原始数据: 01 00 19 08 1A 0F 0F 2B 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:51.694136:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:51.694136:   - 设备类型: LSGControlCenter
2025-08-26 15:14:51.694136:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:51.695133:   - 数据长度: 8
2025-08-26 15:14:51.695133:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:51.695133:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:51.695133:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:51.695133:   - 原始数据: 01 00 19 08 1A 0F 0F 33 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:51.696147:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:51.696147:   - 设备类型: LSGControlCenter
2025-08-26 15:14:51.696147:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:51.697127:   - 数据长度: 8
2025-08-26 15:14:51.697127:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:51.697127:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:51.697127: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:51.697127:   - 发现标签数量: 2
2025-08-26 15:14:51.697127:   - 标签详情:
2025-08-26 15:14:51.698122:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:51.698122:     [1] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:51.698122: RFID扫描: 发现 2 个标签
2025-08-26 15:14:51.698122: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 15:14:51.699120: 🎉 LSGate检测到RFID标签！
2025-08-26 15:14:51.699120: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 43], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:51.699120:   - UID: E004015304F3DD22
2025-08-26 15:14:51.699120:   - Data: E004015304F3DD22
2025-08-26 15:14:51.699120:   - EventType: 1
2025-08-26 15:14:51.699120:   - Direction: 0
2025-08-26 15:14:51.699120:   - Antenna: 1
2025-08-26 15:14:51.700116:   - TagFrequency: 0
2025-08-26 15:14:51.700116: 🏷️ LSGate UID[1]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 51], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:51.700116:   - UID: E004015304F3DD22
2025-08-26 15:14:51.700116:   - Data: E004015304F3DD22
2025-08-26 15:14:51.700116:   - EventType: 1
2025-08-26 15:14:51.700116:   - Direction: 0
2025-08-26 15:14:51.700116:   - Antenna: 1
2025-08-26 15:14:51.701113:   - TagFrequency: 0
2025-08-26 15:14:52.191486: 🔄 开始RFID轮询检查...
2025-08-26 15:14:52.191486: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 15:14:52.191486: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 15:14:52.191486: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 15:14:52.191486: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 15:14:52.193480: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:52.193480:   - 设备句柄: 2528519287680
2025-08-26 15:14:52.193480:   - FetchRecords返回值: 0
2025-08-26 15:14:52.193480:   - 报告数量: 2
2025-08-26 15:14:52.193480:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:52.194477:   - 原始数据: 01 00 19 08 1A 0F 0F 2B 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:52.194477:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:52.194477:   - 设备类型: LSGControlCenter
2025-08-26 15:14:52.194477:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:52.194477:   - 数据长度: 8
2025-08-26 15:14:52.194477:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:52.194477:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:52.195473:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:52.195473:   - 原始数据: 01 00 19 08 1A 0F 0F 33 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:52.195473:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:52.195473:   - 设备类型: LSGControlCenter
2025-08-26 15:14:52.195473:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:52.195473:   - 数据长度: 8
2025-08-26 15:14:52.195473:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:52.196470:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:52.196470: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:52.196470:   - 发现标签数量: 2
2025-08-26 15:14:52.196470:   - 标签详情:
2025-08-26 15:14:52.196470:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:52.196470:     [1] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:52.196470: RFID扫描: 发现 2 个标签
2025-08-26 15:14:52.196470: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 15:14:52.197467: 🎉 LSGate检测到RFID标签！
2025-08-26 15:14:52.197467: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 43], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:52.197467:   - UID: E004015304F3DD22
2025-08-26 15:14:52.197467:   - Data: E004015304F3DD22
2025-08-26 15:14:52.197467:   - EventType: 1
2025-08-26 15:14:52.197467:   - Direction: 0
2025-08-26 15:14:52.197467:   - Antenna: 1
2025-08-26 15:14:52.197467:   - TagFrequency: 0
2025-08-26 15:14:52.198464: 🏷️ LSGate UID[1]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 51], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:52.198464:   - UID: E004015304F3DD22
2025-08-26 15:14:52.198464:   - Data: E004015304F3DD22
2025-08-26 15:14:52.198464:   - EventType: 1
2025-08-26 15:14:52.198464:   - Direction: 0
2025-08-26 15:14:52.198464:   - Antenna: 1
2025-08-26 15:14:52.198464:   - TagFrequency: 0
2025-08-26 15:14:52.691827: 🔄 开始RFID轮询检查...
2025-08-26 15:14:52.691827: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 15:14:52.691827: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 15:14:52.692825: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 15:14:52.692825: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 15:14:52.693821: 🔍 LSGate硬件扫描详情:
2025-08-26 15:14:52.694819:   - 设备句柄: 2528519287680
2025-08-26 15:14:52.694819:   - FetchRecords返回值: 0
2025-08-26 15:14:52.694819:   - 报告数量: 2
2025-08-26 15:14:52.694819:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:52.695815:   - 原始数据: 01 00 19 08 1A 0F 0F 2B 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:52.695815:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:52.695815:   - 设备类型: LSGControlCenter
2025-08-26 15:14:52.695815:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:52.695815:   - 数据长度: 8
2025-08-26 15:14:52.696812:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:52.696812:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:52.696812:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 15:14:52.696812:   - 原始数据: 01 00 19 08 1A 0F 0F 33 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 15:14:52.696812:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 15:14:52.696812:   - 设备类型: LSGControlCenter
2025-08-26 15:14:52.696812:   - 事件类型: 1, 方向: 0
2025-08-26 15:14:52.697808:   - 数据长度: 8
2025-08-26 15:14:52.697808:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 15:14:52.697808:   - 提取的UID: E004015304F3DD22
2025-08-26 15:14:52.697808: 📊 LSGate扫描结果汇总:
2025-08-26 15:14:52.697808:   - 发现标签数量: 2
2025-08-26 15:14:52.697808:   - 标签详情:
2025-08-26 15:14:52.697808:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:52.698805:     [1] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 15:14:52.698805: RFID扫描: 发现 2 个标签
2025-08-26 15:14:52.698805: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 15:14:52.698805: 🎉 LSGate检测到RFID标签！
2025-08-26 15:14:52.698805: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 43], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:52.698805:   - UID: E004015304F3DD22
2025-08-26 15:14:52.698805:   - Data: E004015304F3DD22
2025-08-26 15:14:52.698805:   - EventType: 1
2025-08-26 15:14:52.699801:   - Direction: 0
2025-08-26 15:14:52.699801:   - Antenna: 1
2025-08-26 15:14:52.699801:   - TagFrequency: 0
2025-08-26 15:14:52.699801: 🏷️ LSGate UID[1]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 15, 15, 51], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 15:14:52.699801:   - UID: E004015304F3DD22
2025-08-26 15:14:52.699801:   - Data: E004015304F3DD22
2025-08-26 15:14:52.699801:   - EventType: 1
2025-08-26 15:14:52.699801:   - Direction: 0
2025-08-26 15:14:52.700798:   - Antenna: 1
2025-08-26 15:14:52.700798:   - TagFrequency: 0
